<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥市文旅助手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        header {
            background-color: #0078d7;
            color: white;
            padding: 10px 20px;
            text-align: center;
        }
        .banner {
            display: flex;
            overflow: hidden;
            position: relative;
        }
        .banner img {
            width: 100%;
            height: auto;
        }
        .section {
            padding: 20px;
            background-color: white;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .section h2 {
            margin-top: 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .card {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .card img {
            width: 100%;
            height: 100px;
            object-fit: cover;
        }
        .card-content {
            padding: 10px;
        }
        .card-content h3 {
            margin: 0;
            font-size: 16px;
        }
        .card-content p {
            margin: 5px 0 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <header>
        <h1>合肥市文旅助手</h1>
    </header>

    <div class="banner">
        <img src="https://via.placeholder.com/800x300" alt="骆岗公园">
        <img src="https://via.placeholder.com/800x300" alt="环巢湖片区">
        <img src="https://via.placeholder.com/800x300" alt="庐江山水片区">
    </div>

    <div class="section">
        <h2>智能服务</h2>
        <div class="grid">
            <div class="card">
                <img src="https://via.placeholder.com/150" alt="路径规划">
                <div class="card-content">
                    <h3>路径规划</h3>
                    <p>推荐路线与AI规划</p>
                </div>
            </div>
            <div class="card">
                <img src="https://via.placeholder.com/150" alt="伴游导览">
                <div class="card-content">
                    <h3>伴游导览</h3>
                    <p>语音播报与路线推荐</p>
                </div>
            </div>
            <div class="card">
                <img src="https://via.placeholder.com/150" alt="游记生成">
                <div class="card-content">
                    <h3>游记生成</h3>
                    <p>记录美好瞬间</p>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>合肥旅游圈</h2>
        <p>基于手绘地图，展示特色景点与分类。</p>
    </div>

    <div class="section">
        <h2>推荐活动</h2>
        <div class="grid">
            <div class="card">
                <img src="https://via.placeholder.com/150" alt="活动1">
                <div class="card-content">
                    <h3>活动1</h3>
                    <p>时间：2025年7月20日</p>
                </div>
            </div>
            <div class="card">
                <img src="https://via.placeholder.com/150" alt="活动2">
                <div class="card-content">
                    <h3>活动2</h3>
                    <p>时间：2025年7月21日</p>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>游玩路线推荐</h2>
        <ul>
            <li>两天亲子游路线：动物园、科技馆、融创乐园</li>
            <li>三天文化游路线：三河古镇、三国遗址公园、渡江战役纪念馆、包公园</li>
            <li>两天生态休闲路线：环巢湖</li>
        </ul>
    </div>
</body>
</html>
