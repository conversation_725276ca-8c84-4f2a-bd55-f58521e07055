<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游记生成 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 顶部导航 -->
    <div class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="flex items-center p-4">
            <a href="index.html" class="text-gray-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-lg font-bold flex-1 text-center mr-6">游记生成</h1>
        </div>
    </div>

    <!-- 游记生成表单 -->
    <div class="p-4">
        <div class="bg-white rounded-lg p-4 shadow-sm">
            <h2 class="text-lg font-bold mb-4">创建游记</h2>
            
            <!-- 选择景点 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择游玩景点</label>
                <div class="flex flex-wrap gap-2">
                    <span class="px-3 py-1.5 bg-blue-100 text-blue-600 rounded-full text-sm flex items-center">
                        科学岛
                        <button class="ml-1">×</button>
                    </span>
                    <span class="px-3 py-1.5 bg-blue-100 text-blue-600 rounded-full text-sm flex items-center">
                        合肥创新馆
                        <button class="ml-1">×</button>
                    </span>
                    <button class="px-3 py-1.5 border border-dashed border-gray-300 rounded-full text-sm text-gray-500">
                        + 添加景点
                    </button>
                </div>
            </div>

            <!-- 上传照片 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">上传照片</label>
                <div class="grid grid-cols-4 gap-2">
                    <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1583071299210-c6c25209ee7c?w=200&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                    <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1562774053-701939374585?w=200&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                    <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 游玩体验 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">游玩体验</label>
                <textarea class="w-full h-32 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none" placeholder="简单描述一下你的游玩体验，AI将帮你生成精美游记..."></textarea>
            </div>

            <!-- 游记风格 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">游记风格</label>
                <div class="flex flex-wrap gap-2">
                    <button class="px-3 py-1.5 bg-blue-600 text-white rounded-full text-sm">轻松活泼</button>
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">文艺清新</button>
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">专业详实</button>
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">趣味科普</button>
                </div>
            </div>

            <!-- 生成按钮 -->
            <button class="w-full bg-blue-600 text-white py-3 rounded-lg flex items-center justify-center mt-6">
                <svg class="w-5 h-5 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a8 8 0 01-8 8m8-8a8 8 0 00-8-8m8 8h2m-2 0a8 8 0 01-8 8m8-8h2m-4 4h4m-4-4h4m-6-4h6m-6 4h6m-6-4h6m-6 4h6" />
                </svg>
                AI智能生成游记
            </button>
        </div>

        <!-- 游记预览 -->
        <div class="mt-4 bg-white rounded-lg p-4 shadow-sm">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold">游记预览</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-gray-100 rounded-full">编辑</button>
                    <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-full">分享</button>
                </div>
            </div>
            <div class="prose max-w-none">
                <h3 class="text-xl font-bold mb-2">科技之旅：探索合肥创新之美</h3>
                <div class="text-gray-500 text-sm mb-4">
                    发布于 2025年7月19日 | 浏览 245
                </div>
                <div class="space-y-4">
                    <p class="text-gray-600">
                        今天和家人一起来到了合肥科学岛，这里的科技氛围让人震撼。走进科学岛实验室，孩子们对各种前沿科技展品充满了好奇...
                    </p>
                    <div class="grid grid-cols-2 gap-2">
                        <img src="https://images.unsplash.com/photo-1583071299210-c6c25209ee7c?w=800&auto=format&fit=crop&q=60" class="w-full rounded-lg">
                        <img src="https://images.unsplash.com/photo-1562774053-701939374585?w=800&auto=format&fit=crop&q=60" class="w-full rounded-lg">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <a href="index.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </a>
        <a href="explore.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </a>
        <a href="trip.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </a>
        <a href="profile.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </a>
    </div>
</body>
</html>
