<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能导览 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .map-background {
            background-image: url('https://images.unsplash.com/photo-1524661135-423995f22d0b?w=1600&auto=format&fit=crop&q=60');
            background-size: cover;
            background-position: center;
            position: relative;
            height: 65vh;
        }
        .map-background::after {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: 1;
        }
        .map-content {
            position: relative;
            z-index: 2;
        }
        .spot-marker {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            position: absolute;
            cursor: pointer;
            transition: all 0.3s;
        }
        .spot-marker.active {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
            }
        }
        .bottom-sheet {
            transition: transform 0.3s ease-out;
        }
        .route-card {
            transition: all 0.3s;
        }
        .route-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 顶部导航 -->
    <div class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="flex items-center p-4">
            <a href="index.html" class="text-gray-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-lg font-bold flex-1 text-center mr-6">智能导览</h1>
            <button class="p-2 bg-blue-50 rounded-full">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            </button>
        </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-background">
        <div class="map-content h-full relative">
            <!-- 当前位置标记 -->
            <div class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div class="w-6 h-6 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white px-3 py-1 rounded-full text-sm shadow-md whitespace-nowrap">
                    当前位置
                </div>
            </div>

            <!-- 景点标记 - 科学岛 -->
            <div class="spot-marker active bg-blue-500" style="top: 30%; left: 40%;">
                <div class="hidden group-hover:block absolute -top-20 left-1/2 transform -translate-x-1/2 bg-white p-2 rounded-lg shadow-lg whitespace-nowrap">
                    <strong>科学岛</strong>
                    <p class="text-xs text-gray-500">距离500m</p>
                </div>
            </div>

            <!-- 景点标记 - 创新馆 -->
            <div class="spot-marker bg-green-500" style="top: 45%; left: 60%;">
                <div class="hidden group-hover:block absolute -top-20 left-1/2 transform -translate-x-1/2 bg-white p-2 rounded-lg shadow-lg whitespace-nowrap">
                    <strong>合肥创新馆</strong>
                    <p class="text-xs text-gray-500">距离1.2km</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 语音交互区 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-lg transform translate-y-2/3 bottom-sheet">
        <div class="p-4">
            <!-- 拖动条 -->
            <div class="w-12 h-1 bg-gray-200 rounded-full mx-auto mb-4"></div>

            <!-- 语音输入按钮 -->
            <div class="flex flex-col items-center mb-6">
                <button class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center shadow-lg mb-2">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                </button>
                <span class="text-sm text-gray-500">按住说话</span>
            </div>

            <!-- 快捷问题 -->
            <div class="space-y-2 mb-6">
                <h3 class="font-medium mb-2">快捷问答</h3>
                <div class="flex flex-wrap gap-2">
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">附近有什么景点？</button>
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">最近的厕所在哪？</button>
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">到科学岛怎么走？</button>
                    <button class="px-3 py-1.5 bg-gray-100 rounded-full text-sm">附近有停车场吗？</button>
                </div>
            </div>

            <!-- 推荐路线 -->
            <div>
                <h3 class="font-medium mb-3">推荐路线</h3>
                <div class="space-y-3">
                    <!-- 公交路线 -->
                    <div class="bg-blue-50 p-3 rounded-lg route-card">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">公交路线</h4>
                                <p class="text-sm text-gray-500 mt-1">
                                    25分钟 · 4.2公里 · ￥2
                                </p>
                            </div>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-full text-sm">
                                导航
                            </button>
                        </div>
                        <div class="mt-2 text-sm">
                            <span class="text-gray-500">乘坐：</span>
                            <span>108路 → 步行200米</span>
                        </div>
                    </div>

                    <!-- 步行路线 -->
                    <div class="bg-green-50 p-3 rounded-lg route-card">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">步行路线</h4>
                                <p class="text-sm text-gray-500 mt-1">
                                    15分钟 · 1.2公里
                                </p>
                            </div>
                            <button class="px-3 py-1 bg-green-600 text-white rounded-full text-sm">
                                导航
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 语音播报弹窗 -->
    <div class="fixed inset-0 bg-black/50 hidden" id="voiceModal">
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl p-4">
            <div class="flex items-start mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1583071299210-c6c25209ee7c?w=200&auto=format&fit=crop&q=60" class="w-full h-full object-cover rounded-lg">
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="font-bold">科学岛</h3>
                    <p class="text-sm text-gray-500 mt-1">科技展览 · 互动体验</p>
                </div>
                <button class="p-2">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 010-7.072m12.728 3.536a3 3 0 11-4.243 4.243m-9.9-2.828a9 9 0 010-12.728" />
                        </svg>
                    </button>
                    <div class="text-sm">
                        <p>正在播放语音导览...</p>
                        <p class="text-gray-500">02:35 / 05:00</p>
                    </div>
                </div>
                <button class="px-4 py-2 bg-gray-100 rounded-full text-sm">
                    查看详情
                </button>
            </div>
        </div>
    </div>

    <script>
        // 这里可以添加地图交互、语音识别等功能的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            // 点击景点标记显示语音播报弹窗
            const spotMarkers = document.querySelectorAll('.spot-marker');
            const voiceModal = document.getElementById('voiceModal');
            
            spotMarkers.forEach(marker => {
                marker.addEventListener('click', () => {
                    voiceModal.classList.remove('hidden');
                });
            });

            // 关闭语音播报弹窗
            voiceModal.querySelector('button').addEventListener('click', () => {
                voiceModal.classList.add('hidden');
            });

            // 底部面板上下滑动
            const bottomSheet = document.querySelector('.bottom-sheet');
            let isDragging = false;
            let startY = 0;
            let startTransform = 0;

            bottomSheet.addEventListener('touchstart', (e) => {
                isDragging = true;
                startY = e.touches[0].clientY;
                startTransform = parseInt(getComputedStyle(bottomSheet).transform.split(',')[5]) || 0;
            });

            bottomSheet.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                const deltaY = e.touches[0].clientY - startY;
                const newTransform = Math.max(0, Math.min(deltaY + startTransform, bottomSheet.offsetHeight * 0.6));
                bottomSheet.style.transform = `translateY(${newTransform}px)`;
            });

            bottomSheet.addEventListener('touchend', () => {
                isDragging = false;
            });
        });
    </script>
</body>
</html>
