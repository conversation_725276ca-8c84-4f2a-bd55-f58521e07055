<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行程 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 顶部标题栏 -->
    <div class="bg-white shadow-sm">
        <div class="p-4">
            <h1 class="text-lg font-bold text-center">我的行程</h1>
        </div>
        <div class="flex border-b">
            <button class="flex-1 py-3 text-center text-blue-600 border-b-2 border-blue-600">已规划行程</button>
            <button class="flex-1 py-3 text-center text-gray-500">历史行程</button>
        </div>
    </div>

    <!-- 行程列表 -->
    <div class="p-4 space-y-4">
        <!-- 行程项 -->
        <div class="bg-white rounded-lg overflow-hidden shadow-sm">
            <div class="relative aspect-[2/1]">
                <img src="https://images.unsplash.com/photo-1625744423474-3f8bb5dd5847?w=800&auto=format&fit=crop&q=60" class="w-full h-full object-cover" alt="包河公园">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex flex-col justify-end p-4 text-white">
                    <h3 class="font-medium">合肥文化探索一日游</h3>
                    <p class="text-sm mt-1">7月21日出发</p>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>约6小时</span>
                    <span class="mx-2">·</span>
                    <span>3个景点</span>
                    <span class="mx-2">·</span>
                    <span>￥120/人</span>
                </div>
                <div class="flex mt-3 space-x-2">
                    <div class="flex-1">
                        <div class="text-xs text-gray-500">行程路线</div>
                        <div class="flex items-center mt-1 text-sm">
                            <span>包河公园</span>
                            <svg class="w-3 h-3 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>三河古镇</span>
                            <svg class="w-3 h-3 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>中科大</span>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-blue-600 text-white text-sm rounded-full">开始导航</button>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg overflow-hidden shadow-sm">
            <div class="relative aspect-[2/1]">
                <img src="https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?w=800&auto=format&fit=crop&q=60" class="w-full h-full object-cover" alt="巢湖">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex flex-col justify-end p-4 text-white">
                    <h3 class="font-medium">巢湖生态休闲游</h3>
                    <p class="text-sm mt-1">7月25日出发</p>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>约4小时</span>
                    <span class="mx-2">·</span>
                    <span>2个景点</span>
                    <span class="mx-2">·</span>
                    <span>￥80/人</span>
                </div>
                <div class="flex mt-3 space-x-2">
                    <div class="flex-1">
                        <div class="text-xs text-gray-500">行程路线</div>
                        <div class="flex items-center mt-1 text-sm">
                            <span>巢湖风景区</span>
                            <svg class="w-3 h-3 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>半汤温泉</span>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-blue-600 text-white text-sm rounded-full">开始导航</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新建行程入口 -->
    <div class="fixed right-4 bottom-20">
        <button class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
        </button>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <a href="index.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </a>
        <a href="explore.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </a>
        <a href="trip.html" class="text-center text-blue-600">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </a>
        <a href="profile.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </a>
    </div>
</body>
</html>
