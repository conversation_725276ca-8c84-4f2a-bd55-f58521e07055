<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能导览 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 顶部导航 -->
    <div class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="flex items-center p-4">
            <a href="index.html" class="text-gray-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-lg font-bold flex-1 text-center mr-6">智能导览</h1>
        </div>
    </div>

    <!-- 地图区域 -->
    <div class="relative bg-blue-50" style="height: 40vh;">
        <img src="https://images.unsplash.com/photo-1524661135-423995f22d0b?w=800&auto=format&fit=crop&q=60" alt="地图" class="w-full h-full object-cover">
        <div class="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur rounded-lg p-3">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="font-medium">当前位置</h3>
                    <p class="text-sm text-gray-500">距离科学岛 2.3km</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 语音交互区 -->
    <div class="p-4 bg-white">
        <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                </div>
                <span class="ml-2 text-sm text-gray-500">点击按钮开始语音导览</span>
            </div>
            <button class="w-full bg-blue-600 text-white py-3 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                开始语音导览
            </button>
        </div>
    </div>

    <!-- 周边推荐 -->
    <div class="p-4">
        <h2 class="text-lg font-bold mb-3">周边推荐</h2>
        <div class="space-y-4">
            <div class="bg-white rounded-lg overflow-hidden shadow-sm">
                <img src="https://images.unsplash.com/photo-1583071299210-c6c25209ee7c?w=800&auto=format&fit=crop&q=60" class="w-full h-32 object-cover" alt="科学岛">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <h3 class="font-medium">科学岛实验室</h3>
                        <span class="text-sm text-gray-500">2.3km</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">开放时间：09:00-17:00</p>
                    <div class="mt-2 flex space-x-2">
                        <span class="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded-full">科技展览</span>
                        <span class="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded-full">可预约参观</span>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="flex-1 py-2 bg-blue-600 text-white rounded-lg">导航前往</button>
                        <button class="flex-1 py-2 bg-gray-100 rounded-lg">详情介绍</button>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg overflow-hidden shadow-sm">
                <img src="https://images.unsplash.com/photo-1562774053-701939374585?w=800&auto=format&fit=crop&q=60" class="w-full h-32 object-cover" alt="创新馆">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <h3 class="font-medium">合肥创新馆</h3>
                        <span class="text-sm text-gray-500">3.1km</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">开放时间：10:00-18:00</p>
                    <div class="mt-2 flex space-x-2">
                        <span class="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded-full">科技互动</span>
                        <span class="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded-full">亲子科普</span>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="flex-1 py-2 bg-blue-600 text-white rounded-lg">导航前往</button>
                        <button class="flex-1 py-2 bg-gray-100 rounded-lg">详情介绍</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <a href="index.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </a>
        <a href="explore.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </a>
        <a href="trip.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </a>
        <a href="profile.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </a>
    </div>
</body>
</html>
