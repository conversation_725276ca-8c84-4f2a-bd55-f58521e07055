<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>探索 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 顶部分类导航 -->
    <div class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="flex items-center p-4">
            <div class="flex-1 bg-gray-100 rounded-full px-4 py-2 flex items-center">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <input type="text" placeholder="搜索景点、美食、酒店" class="ml-2 bg-transparent w-full outline-none text-sm">
            </div>
        </div>
        <div class="flex overflow-x-auto p-2 space-x-4 whitespace-nowrap">
            <button class="px-4 py-2 text-sm bg-blue-600 text-white rounded-full">景点</button>
            <button class="px-4 py-2 text-sm bg-gray-100 rounded-full">美食</button>
            <button class="px-4 py-2 text-sm bg-gray-100 rounded-full">酒店</button>
            <button class="px-4 py-2 text-sm bg-gray-100 rounded-full">攻略</button>
            <button class="px-4 py-2 text-sm bg-gray-100 rounded-full">游记</button>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="flex p-3 bg-white border-b text-sm space-x-4">
        <button class="flex items-center text-gray-600">
            <span>全部分类</span>
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
        </button>
        <button class="flex items-center text-gray-600">
            <span>距离优先</span>
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
        </button>
        <button class="flex items-center text-gray-600">
            <span>筛选</span>
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
        </button>
    </div>

    <!-- 景点列表 -->
    <div class="space-y-3 p-4">
        <!-- 景点项 -->
        <div class="bg-white rounded-lg overflow-hidden shadow-sm">
            <img src="https://images.unsplash.com/photo-1625744423474-3f8bb5dd5847?w=800&auto=format&fit=crop&q=60" class="w-full h-48 object-cover" alt="包河公园">
            <div class="p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="font-medium text-lg">包河公园</h3>
                        <div class="flex items-center mt-1">
                            <div class="flex text-yellow-400">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                                <span class="ml-1 text-sm">4.8</span>
                            </div>
                            <span class="mx-2 text-gray-300">|</span>
                            <span class="text-sm text-gray-500">2.3km</span>
                        </div>
                    </div>
                    <span class="text-blue-600 text-sm">免费</span>
                </div>
                <p class="text-sm text-gray-500 mt-2 line-clamp-2">城市中心的生态绿地，环境优美，适合休闲散步，园内设施完善，交通便利。</p>
                <div class="flex mt-3 text-xs">
                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full mr-2">生态公园</span>
                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full mr-2">亲子游玩</span>
                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full">休闲娱乐</span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg overflow-hidden shadow-sm">
            <img src="https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?w=800&auto=format&fit=crop&q=60" class="w-full h-48 object-cover" alt="三河古镇">
            <div class="p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="font-medium text-lg">三河古镇</h3>
                        <div class="flex items-center mt-1">
                            <div class="flex text-yellow-400">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                                <span class="ml-1 text-sm">4.9</span>
                            </div>
                            <span class="mx-2 text-gray-300">|</span>
                            <span class="text-sm text-gray-500">5.8km</span>
                        </div>
                    </div>
                    <span class="text-blue-600 text-sm">￥60起</span>
                </div>
                <p class="text-sm text-gray-500 mt-2 line-clamp-2">千年古镇，保存完好的明清建筑群，独特的徽派建筑风格，深厚的历史文化底蕴。</p>
                <div class="flex mt-3 text-xs">
                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full mr-2">历史古镇</span>
                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full mr-2">文化遗产</span>
                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full">特色建筑</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <a href="index.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </a>
        <a href="explore.html" class="text-center text-blue-600">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </a>
        <a href="trip.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </a>
        <a href="profile.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </a>
    </div>
</body>
</html>
