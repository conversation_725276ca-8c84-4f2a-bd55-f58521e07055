<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路线规划 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .scrolling-touch {
            -webkit-overflow-scrolling: touch;
        }
        .map-background {
            background-image: url('https://images.unsplash.com/photo-1524661135-423995f22d0b?w=1600&auto=format&fit=crop&q=60');
            background-size: cover;
            background-position: center;
            position: relative;
        }
        .map-background::after {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(255, 255, 255, 0.9);
            z-index: 1;
        }
        .map-content {
            position: relative;
            z-index: 2;
        }
    </style>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 顶部导航 -->
    <div class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="flex items-center p-4">
            <a href="index.html" class="text-gray-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-lg font-bold flex-1 text-center mr-6">路线规划</h1>
        </div>
    </div>

    <!-- AI规划器 -->
    <div class="p-4 bg-white">
        <div class="bg-blue-50 rounded-lg p-4">
            <h3 class="font-medium mb-3">AI智能规划</h3>
            <div class="space-y-3">
                <div class="flex items-center">
                    <span class="w-20 text-sm text-gray-500">出行天数</span>
                    <input type="number" class="flex-1 ml-2 p-2 border rounded" placeholder="请输入天数" min="1">
                </div>
                <div class="flex items-center">
                    <span class="w-20 text-sm text-gray-500">出行人数</span>
                    <input type="number" class="flex-1 ml-2 p-2 border rounded" placeholder="请输入人数" min="1">
                </div>
                <div class="flex items-center">
                    <span class="w-20 text-sm text-gray-500">预算范围</span>
                    <input type="number" class="flex-1 ml-2 p-2 border rounded" placeholder="请输入预算/人">
                </div>
                <div class="flex items-center">
                    <span class="w-20 text-sm text-gray-500">兴趣偏好</span>
                    <div class="flex-1 ml-2 flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-full">科技文化</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 rounded-full">生态休闲</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 rounded-full">历史人文</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 rounded-full">美食购物</button>
                    </div>
                </div>
                <button class="w-full bg-blue-600 text-white py-3 rounded-lg mt-4">
                    开始智能规划
                </button>
            </div>
        </div>
    </div>

    <!-- 推荐线路 -->
    <div class="p-4">
        <h2 class="text-lg font-bold mb-3">精选主题线路</h2>
        <div class="space-y-4">
            <div class="bg-white rounded-lg overflow-hidden shadow-sm">
                <img src="https://images.unsplash.com/photo-1583071299210-c6c25209ee7c?w=800&auto=format&fit=crop&q=60" class="w-full h-40 object-cover" alt="科创探索">
                <div class="p-4">
                    <h3 class="font-medium">科创探索一日游</h3>
                    <div class="flex items-center mt-2 text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>约6小时</span>
                        <span class="mx-2">·</span>
                        <span>3个景点</span>
                        <span class="mx-2">·</span>
                        <span>￥120/人</span>
                    </div>
                    <div class="mt-3 text-sm text-gray-600">
                        <p>路线：科学岛 → 中科大 → 创新馆</p>
                    </div>
                    <button class="mt-3 w-full bg-blue-600 text-white py-2 rounded-lg">查看详情</button>
                </div>
            </div>

            <div class="bg-white rounded-lg overflow-hidden shadow-sm">
                <img src="https://images.unsplash.com/photo-1604328698692-f76ea9498e76?w=800&auto=format&fit=crop&q=60" class="w-full h-40 object-cover" alt="文化寻迹">
                <div class="p-4">
                    <h3 class="font-medium">文化寻迹半日游</h3>
                    <div class="flex items-center mt-2 text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>约4小时</span>
                        <span class="mx-2">·</span>
                        <span>2个景点</span>
                        <span class="mx-2">·</span>
                        <span>￥80/人</span>
                    </div>
                    <div class="mt-3 text-sm text-gray-600">
                        <p>路线：三河古镇 → 包河公园</p>
                    </div>
                    <button class="mt-3 w-full bg-blue-600 text-white py-2 rounded-lg">查看详情</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <a href="index.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </a>
        <a href="explore.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </a>
        <a href="trip.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </a>
        <a href="profile.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </a>
    </div>
</body>
</html>
