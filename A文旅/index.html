<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .shadow-text {
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 顶部搜索栏 -->
    <div class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="flex items-center p-4">
            <div class="flex-1 bg-gray-100 rounded-full px-4 py-2 flex items-center">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <input type="text" placeholder="搜索景点、美食、活动" class="ml-2 bg-transparent w-full outline-none text-sm">
            </div>
        </div>
    </div>

    <!-- 轮播区域 -->
    <div class="relative bg-blue-50">
        <div class="aspect-[16/9] relative overflow-hidden">
            <img src="https://images.unsplash.com/photo-1645956734658-8b6e62e7d35a?w=800&auto=format&fit=crop&q=60" alt="骆岗公园" class="w-full h-full object-cover">
            <div class="absolute bottom-8 left-4 right-4 text-white">
                <h2 class="text-xl font-bold shadow-text">骆岗公园生态景区</h2>
                <p class="text-sm mt-1 shadow-text">体验合肥最美生态休闲胜地</p>
            </div>
            <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                <div class="w-2 h-2 rounded-full bg-blue-600"></div>
                <div class="w-2 h-2 rounded-full bg-white opacity-60"></div>
                <div class="w-2 h-2 rounded-full bg-white opacity-60"></div>
            </div>
        </div>
    </div>

    <!-- 智能服务工具 -->
    <div class="p-4 bg-white">
        <h2 class="text-lg font-bold mb-3">智能服务</h2>
        <div class="grid grid-cols-4 gap-4">
            <a href="route-plan.html" class="text-center">
                <div class="w-14 h-14 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                    </svg>
                </div>
                <p class="mt-1 text-sm">路线规划</p>
            </a>
            <a href="smart-guide.html" class="text-center">
                <div class="w-14 h-14 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <p class="mt-1 text-sm">智能导览</p>
            </a>
            <a href="travel-note.html" class="text-center">
                <div class="w-14 h-14 mx-auto bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                </div>
                <p class="mt-1 text-sm">游记生成</p>
            </a>
            <a href="more-services.html" class="text-center">
                <div class="w-14 h-14 mx-auto bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <p class="mt-1 text-sm">更多服务</p>
            </a>
        </div>
    </div>

    <!-- 合肥旅游圈地图 -->
    <div class="p-4 bg-white mt-2">
        <div class="flex justify-between items-center mb-3">
            <h2 class="text-lg font-bold">旅游地图</h2>
            <div class="flex space-x-2">
                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-full">全部</button>
                <button class="px-3 py-1 text-sm bg-gray-100 rounded-full">特色旅游</button>
                <button class="px-3 py-1 text-sm bg-gray-100 rounded-full">文化景点</button>
            </div>
        </div>
        <div class="relative h-48 bg-blue-50 rounded-lg overflow-hidden">
            <img src="https://images.unsplash.com/photo-1524661135-423995f22d0b?w=800&auto=format&fit=crop&q=60" alt="合肥旅游地图" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-black/10 flex items-center justify-center">
                <button class="px-4 py-2 bg-white rounded-full text-sm font-medium shadow-lg">
                    打开完整地图
                </button>
            </div>
        </div>
    </div>

    <!-- 特色活动 -->
    <div class="p-4 bg-white mt-2">
        <div class="flex justify-between items-center mb-3">
            <h2 class="text-lg font-bold">特色活动</h2>
            <span class="text-sm text-gray-500">更多</span>
        </div>
        <div class="space-y-3">
            <div class="flex space-x-3">
                <div class="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1638132704795-6bb223151bf7?w=200&auto=format&fit=crop&q=60" alt="活动图片" class="w-full h-full object-cover">
                </div>
                <div class="flex-1">
                    <h3 class="font-medium">2025年合肥科技文化节</h3>
                    <p class="text-sm text-gray-500 mt-1">时间：7月20日-7月25日</p>
                    <div class="mt-2">
                        <span class="text-xs px-2 py-1 bg-green-100 text-green-600 rounded-full">进行中</span>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <div class="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1657664066011-ed54a1be81c2?w=200&auto=format&fit=crop&q=60" alt="活动图片" class="w-full h-full object-cover">
                </div>
                <div class="flex-1">
                    <h3 class="font-medium">巢湖之夜音乐节</h3>
                    <p class="text-sm text-gray-500 mt-1">时间：7月30日-8月1日</p>
                    <div class="mt-2">
                        <span class="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded-full">即将开始</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 经典路线 -->
    <div class="p-4 bg-white mt-2">
        <div class="flex justify-between items-center mb-3">
            <h2 class="text-lg font-bold">经典路线</h2>
            <span class="text-sm text-gray-500">更多</span>
        </div>
        <div class="space-y-4">
            <div class="bg-blue-50 p-3 rounded-lg">
                <h3 class="text-blue-600 font-medium">文化探索一日游</h3>
                <div class="flex items-center mt-2 text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>约6小时</span>
                    <span class="mx-2">|</span>
                    <span>￥120/人起</span>
                </div>
                <div class="flex mt-2 space-x-2">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1599707367072-cd6ada2bc375?w=100&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                    <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1584652646003-b823d8f2d80e?w=100&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                    <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=100&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                </div>
            </div>
            <div class="bg-green-50 p-3 rounded-lg">
                <h3 class="text-green-600 font-medium">生态亲子游</h3>
                <div class="flex items-center mt-2 text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>约4小时</span>
                    <span class="mx-2">|</span>
                    <span>￥80/人起</span>
                </div>
                <div class="flex mt-2 space-x-2">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1635886840360-a2157c1f4eb4?w=100&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                    <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612521564730-c8d9a9c60fc7?w=100&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                    <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1477332552946-cfb384aeaf1c?w=100&auto=format&fit=crop&q=60" class="w-full h-full object-cover">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <div class="text-center text-blue-600">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </div>
        <div class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </div>
        <div class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </div>
        <div class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </div>
    </div>
</body>
</html>
