<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 合肥市文旅助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800 pb-16">
    <!-- 个人信息卡片 -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-400 text-white p-6">
        <div class="flex items-center">
            <div class="w-16 h-16 rounded-full overflow-hidden bg-white/20">
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=200&auto=format&fit=crop&q=60" class="w-full h-full object-cover" alt="用户头像">
            </div>
            <div class="ml-4 flex-1">
                <h2 class="font-medium text-lg">张三</h2>
                <p class="text-sm text-white/80 mt-1">合肥旅游达人</p>
            </div>
            <button class="px-3 py-1 border border-white/30 rounded text-sm">编辑资料</button>
        </div>
        <div class="flex justify-around mt-6 text-center">
            <div>
                <div class="text-lg font-medium">8</div>
                <div class="text-sm text-white/80">已发游记</div>
            </div>
            <div>
                <div class="text-lg font-medium">12</div>
                <div class="text-sm text-white/80">收藏景点</div>
            </div>
            <div>
                <div class="text-lg font-medium">2680</div>
                <div class="text-sm text-white/80">积分</div>
            </div>
        </div>
    </div>

    <!-- 功能菜单 -->
    <div class="bg-white mt-3">
        <div class="p-4 border-b">
            <h3 class="text-lg font-medium">我的内容</h3>
        </div>
        <div class="grid grid-cols-4 p-4 gap-4">
            <div class="text-center">
                <div class="w-12 h-12 mx-auto bg-blue-50 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                </div>
                <span class="text-sm mt-1 block">游记</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 mx-auto bg-green-50 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                </div>
                <span class="text-sm mt-1 block">收藏</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 mx-auto bg-yellow-50 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <span class="text-sm mt-1 block">积分</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 mx-auto bg-purple-50 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <span class="text-sm mt-1 block">订单</span>
            </div>
        </div>
    </div>

    <!-- 我的游记 -->
    <div class="bg-white mt-3">
        <div class="p-4 border-b flex justify-between items-center">
            <h3 class="text-lg font-medium">我的游记</h3>
            <span class="text-sm text-blue-600">全部</span>
        </div>
        <div class="p-4 space-y-4">
            <div class="flex space-x-3">
                <div class="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1625744423474-3f8bb5dd5847?w=200&auto=format&fit=crop&q=60" class="w-full h-full object-cover" alt="包河公园游记">
                </div>
                <div class="flex-1">
                    <h4 class="font-medium">漫步包河公园，感受合肥的绿色生态</h4>
                    <p class="text-sm text-gray-500 mt-1 line-clamp-2">春日的包河公园，处处充满生机。漫步其中，欣赏美景，感受合肥这座城市的独特魅力...</p>
                    <div class="flex items-center mt-2 text-xs text-gray-400">
                        <span>2025-07-15</span>
                        <span class="mx-2">·</span>
                        <span>328 阅读</span>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <div class="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?w=200&auto=format&fit=crop&q=60" class="w-full h-full object-cover" alt="巢湖游记">
                </div>
                <div class="flex-1">
                    <h4 class="font-medium">巢湖之夜，星空下的音乐盛宴</h4>
                    <p class="text-sm text-gray-500 mt-1 line-clamp-2">夜幕降临的巢湖畔，伴随着动人的音乐，与好友一起感受这个夏夜的美好时光...</p>
                    <div class="flex items-center mt-2 text-xs text-gray-400">
                        <span>2025-07-10</span>
                        <span class="mx-2">·</span>
                        <span>256 阅读</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通用设置 -->
    <div class="bg-white mt-3">
        <div class="p-4">
            <h3 class="text-lg font-medium">通用设置</h3>
        </div>
        <div class="space-y-1">
            <button class="w-full p-4 flex items-center justify-between text-gray-600">
                <span>消息通知</span>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
            <button class="w-full p-4 flex items-center justify-between text-gray-600">
                <span>隐私设置</span>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
            <button class="w-full p-4 flex items-center justify-between text-gray-600">
                <span>账号安全</span>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
            <button class="w-full p-4 flex items-center justify-between text-gray-600">
                <span>关于我们</span>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex items-center justify-around p-2">
        <a href="index.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="text-xs">首页</span>
        </a>
        <a href="explore.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="text-xs">探索</span>
        </a>
        <a href="trip.html" class="text-center text-gray-400">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="text-xs">行程</span>
        </a>
        <a href="profile.html" class="text-center text-blue-600">
            <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-xs">我的</span>
        </a>
    </div>
</body>
</html>
