1.首页：
   1.1.banner：体现骆岗公园（文化集中区），环巢湖片区（生态休闲），庐江山水片区（康养度假）三大生态宣传图，轮播切换
   1.2.智能服务：路径规划，伴游导览，游记生成；三个模快横向展示，空间不能占太大。
        A.路径规划：背景是合肥手绘地图，有推荐路线，AI路径规划
          a.默认展示推荐路线,点击推荐路线，显示两日亲子游，三天文化游，两天生态休闲游，一天骆岗游，两天康养度假路线，点击具体路线，在地图上展示路线上多有景点，并在最下方横向展示路线名称，总的费用，途经点信息（图片，名称，标签，费用），途经点横向展示，可以滑动
           b.可以基于大模型自动规划路线。用户要输入游玩时间，人数，类型，预算，其他需求，自动生成路线，包括起点，途经点，终点的游玩时间，介绍和标签，涉及费用的要把费用列出来；
           c.路线生成后，可对途经点进行删除和新增操作，能够一键付费，能够保存行程
        B.伴游导览：
            a.结合手绘地图，根据当前位置展示附近的景点，若用户进入某景点，地图上对应景点图标闪烁，用户点击，即可自动语音播报并介绍。
            b.能够根据语音交互，输出最近的场馆，景点，设施； 能够选中目标点进行，推荐最优路线，并提供不同交通工具的路线推荐
   1.3.合肥旅游圈：基于合肥手绘地图，地图上标记特色旅游类（骆岗公园片区，环巢湖生态片区，庐江山水片区），历史文化类（三河古镇，包公园），亲子与科技类（动物园，合肥科技馆）等，可以根据类型切换。点击单个景区可在地图聚焦，并介绍景区信息，再点击及进入对应景区页面。
    1.4.推荐活动：合肥及各景区的特色活动展示，包括图片，名称，时间，状态，只展示进行中以及即将进行的；默认展示三个，横向展示，空间不用太大
    1.5.游玩路线推荐：包括两天亲子游路线（动物园，科技馆，融创乐园），三天文化游路线（三河古镇，三国遗址公园，渡江战役纪念馆，包公园），两天生态休闲路线（环巢湖），一天骆岗旅游路线，一天康养度假路线（庐江山水）
    
 2.探索
   探索包括景点，美食，酒店，攻略，游记 ，切换展示；
     2.1.景点：景点展示（图片，名称，介绍，距离，费用，时间，评价），点击可进入对应景区的详情页面，支持按照类型，距离，名称进行筛选
     2.2.美食：展示美食图片，类型，距离，人均，标签，时间，支持根据类型进行筛选
     2.3.酒店、攻略，游记同上，游记和攻略，要能看到观看次数，点赞次数，不要评论

 3.伴游助手  ：点击即可跳转到问答页面。推荐一些常见问题。可以选择不同景点进行问答

  4. 行程：行程规划-调用路径规划；个人行程管理-能够查看所有行程，点击单个行程要能够展开，能够看到行程点，时间，能够对行程进行修改和删除；

  5.我的：展示个人信息，游记数，积分；游记：可以查看发布的游记（图片，名称，时间），订单（通过此平台支付的订单，若涉及二维码，可以直接打开，如骆岗无人巴士）
