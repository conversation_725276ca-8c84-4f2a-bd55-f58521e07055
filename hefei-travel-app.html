<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 顶部导航 */
        .header {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            padding: 20px 16px 16px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .header .subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 4px;
        }
        
        /* Banner轮播 */
        .banner-container {
            position: relative;
            height: 180px;
            overflow: hidden;
        }
        
        .banner-slide {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .banner-slide.active {
            opacity: 1;
        }
        
        .banner-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .banner-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.6));
            color: white;
            padding: 20px 16px 16px;
        }
        
        .banner-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .banner-desc {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .banner-dots {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 6px;
        }
        
        .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .dot.active {
            background: white;
        }
        
        /* 智能服务 */
        .smart-services {
            padding: 20px 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2196f3;
        }
        
        .services-grid {
            display: flex;
            gap: 12px;
        }
        
        .service-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .service-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 8px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .service-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        /* 合肥旅游圈 */
        .tourism-circle {
            padding: 0 16px 20px;
        }
        
        .map-container {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .map-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-btn {
            padding: 6px 12px;
            border-radius: 16px;
            background: #f5f5f5;
            border: none;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-btn.active {
            background: #2196f3;
            color: white;
        }
        
        .map-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .map-point {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #ff4444;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }
        
        .point-1 { top: 30%; left: 40%; }
        .point-2 { top: 60%; left: 70%; }
        .point-3 { top: 45%; left: 20%; }
        .point-4 { top: 70%; left: 45%; }
        
        /* 推荐活动 */
        .activities {
            padding: 0 16px 20px;
        }
        
        .activity-list {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .activity-card {
            min-width: 200px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .activity-image {
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, #4caf50, #2196f3);
            position: relative;
        }
        
        .activity-status {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4444;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
        }
        
        .activity-info {
            padding: 12px;
        }
        
        .activity-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #666;
        }
        
        /* 游玩路线推荐 */
        .routes {
            padding: 0 16px 20px;
        }
        
        .route-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .route-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .route-card:hover {
            transform: translateY(-2px);
        }
        
        .route-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .route-name {
            font-size: 16px;
            font-weight: 600;
            color: #2196f3;
        }
        
        .route-duration {
            font-size: 12px;
            color: #666;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 8px;
        }
        
        .route-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            padding: 8px 0;
            z-index: 1000;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: #2196f3;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        /* 页面内容 */
        .page-content {
            display: none;
            padding: 20px 16px 80px;
        }
        
        .page-content.active {
            display: block;
        }
        
        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 20px;
            width: 90%;
            max-width: 350px;
            max-height: 80vh;
            overflow-y: auto;
        }

        /* 路径规划专用全屏模态框 */
        .route-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 2000;
            overflow-y: auto;
        }

        .route-modal-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .route-header {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            padding: 20px 16px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .route-back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .route-title {
            font-size: 18px;
            font-weight: 600;
        }

        .route-map-container {
            position: relative;
            height: 300px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            background-image:
                radial-gradient(circle at 30% 40%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="%23ddd" stroke-width="1" opacity="0.3"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/><path d="M50 150 Q100 130 150 150 T250 170 Q300 180 350 160" stroke="%234caf50" stroke-width="3" fill="none" opacity="0.6"/><path d="M80 200 Q130 220 180 200 T280 210" stroke="%232196f3" stroke-width="3" fill="none" opacity="0.6"/><circle cx="120" cy="100" r="6" fill="%23ff6b6b" opacity="0.8"/><circle cx="220" cy="180" r="6" fill="%23ff6b6b" opacity="0.8"/><circle cx="320" cy="140" r="6" fill="%23ff6b6b" opacity="0.8"/><text x="130" y="105" font-size="8" fill="%23666">骆岗公园</text><text x="230" y="185" font-size="8" fill="%23666">科技馆</text><text x="330" y="145" font-size="8" fill="%23666">三河古镇</text><text x="20" y="30" font-size="12" fill="%234caf50" font-weight="bold">合肥市手绘地图</text></svg>');
            background-size: cover;
            background-position: center;
            border-bottom: 1px solid #eee;
        }

        .route-point {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff4444;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .route-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #2196f3, #4caf50);
            border-radius: 2px;
        }

        .route-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #eee;
        }

        .route-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: none;
            border: none;
            font-size: 14px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }

        .route-tab.active {
            color: #2196f3;
            border-bottom-color: #2196f3;
        }

        .route-content {
            padding: 16px;
        }

        .route-list-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .route-list-item:hover {
            transform: translateY(-1px);
        }

        .route-list-item.selected {
            border: 2px solid #2196f3;
            background: #f3f9ff;
        }

        .route-bottom-panel {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #eee;
            padding: 16px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            max-height: 50vh;
            overflow-y: auto;
        }

        .route-bottom-panel.show {
            transform: translateY(0);
        }

        .route-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .route-info-title {
            font-size: 18px;
            font-weight: 600;
            color: #2196f3;
        }

        .route-info-price {
            font-size: 16px;
            font-weight: 600;
            color: #ff4444;
        }

        .route-spots-container {
            margin-bottom: 16px;
        }

        .route-spots-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .route-spot-card {
            min-width: 120px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 8px;
            text-align: center;
            position: relative;
        }

        .route-spot-image {
            width: 100%;
            height: 60px;
            background: linear-gradient(135deg, #4caf50, #2196f3);
            border-radius: 4px;
            margin-bottom: 6px;
            position: relative;
        }

        .route-spot-name {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .route-spot-tag {
            font-size: 10px;
            color: #666;
            background: #e0e0e0;
            padding: 2px 6px;
            border-radius: 8px;
            margin-bottom: 2px;
        }

        .route-spot-price {
            font-size: 11px;
            color: #ff4444;
            font-weight: 500;
        }

        .route-spot-delete {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 16px;
            height: 16px;
            background: #f44336;
            color: white;
            border-radius: 50%;
            border: none;
            font-size: 10px;
            cursor: pointer;
            display: none;
        }

        .route-spot-card:hover .route-spot-delete {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .route-actions {
            display: flex;
            gap: 8px;
        }

        .route-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }

        .route-btn-primary {
            background: #2196f3;
            color: white;
        }

        .route-btn-primary:hover {
            background: #1976d2;
        }

        .route-btn-secondary {
            background: #4caf50;
            color: white;
        }

        .route-btn-secondary:hover {
            background: #388e3c;
        }

        .route-btn-outline {
            background: white;
            color: #2196f3;
            border: 1px solid #2196f3;
        }

        .route-btn-outline:hover {
            background: #f3f9ff;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }
        
        /* 响应式 */
        @media (max-width: 375px) {
            .services-grid {
                gap: 8px;
            }
            
            .service-card {
                padding: 12px 8px;
            }
            
            .service-icon {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
            
            .service-name {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 首页内容 -->
        <div id="home-page" class="page-content active">
            <!-- 顶部导航 -->
            <div class="header">
                <h1>合肥文旅助手</h1>
                <div class="subtitle">打造一站式旅游服务与体验平台</div>
            </div>
            
            <!-- Banner轮播 -->
            <div class="banner-container">
                <div class="banner-slide active">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop" alt="骆岗公园">
                    <div class="banner-overlay">
                        <div class="banner-title">骆岗公园</div>
                        <div class="banner-desc">文化集中区 · 城市绿肺</div>
                    </div>
                </div>
                <div class="banner-slide">
                    <img src="https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800&h=400&fit=crop" alt="环巢湖片区">
                    <div class="banner-overlay">
                        <div class="banner-title">环巢湖片区</div>
                        <div class="banner-desc">生态休闲 · 湖光山色</div>
                    </div>
                </div>
                <div class="banner-slide">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop" alt="庐江山水片区">
                    <div class="banner-overlay">
                        <div class="banner-title">庐江山水片区</div>
                        <div class="banner-desc">康养度假 · 山水怡人</div>
                    </div>
                </div>
                <div class="banner-dots">
                    <div class="dot active" onclick="showSlide(0)"></div>
                    <div class="dot" onclick="showSlide(1)"></div>
                    <div class="dot" onclick="showSlide(2)"></div>
                </div>
            </div>

            <!-- 智能服务 -->
            <div class="smart-services">
                <div class="section-title">🤖 智能服务</div>
                <div class="services-grid">
                    <div class="service-card" onclick="openRouteModal()">
                        <div class="service-icon">🗺️</div>
                        <div class="service-name">路径规划</div>
                    </div>
                    <div class="service-card" onclick="openGuideModal()">
                        <div class="service-icon">🎧</div>
                        <div class="service-name">伴游导览</div>
                    </div>
                    <div class="service-card" onclick="openTravelLogModal()">
                        <div class="service-icon">📝</div>
                        <div class="service-name">游记生成</div>
                    </div>
                </div>
            </div>

            <!-- 合肥旅游圈 -->
            <div class="tourism-circle">
                <div class="section-title">🌟 合肥旅游圈</div>
                <div class="map-container">
                    <div class="map-filters">
                        <button class="filter-btn active" onclick="filterMap('all')">全部</button>
                        <button class="filter-btn" onclick="filterMap('scenic')">特色旅游</button>
                        <button class="filter-btn" onclick="filterMap('culture')">历史文化</button>
                        <button class="filter-btn" onclick="filterMap('family')">亲子科技</button>
                    </div>
                    <div class="map-image">
                        <div class="map-point point-1" onclick="showSpotInfo('骆岗公园', '文化集中区，城市绿肺，适合休闲漫步')"></div>
                        <div class="map-point point-2" onclick="showSpotInfo('环巢湖', '生态休闲胜地，湖光山色美不胜收')"></div>
                        <div class="map-point point-3" onclick="showSpotInfo('三河古镇', '千年古镇，历史文化底蕴深厚')"></div>
                        <div class="map-point point-4" onclick="showSpotInfo('合肥科技馆', '科技体验，亲子互动的好去处')"></div>
                    </div>
                </div>
            </div>

            <!-- 推荐活动 -->
            <div class="activities">
                <div class="section-title">🎉 推荐活动</div>
                <div class="activity-list">
                    <div class="activity-card" onclick="showActivityDetail('春季赏花节')">
                        <div class="activity-image">
                            <div class="activity-status">进行中</div>
                        </div>
                        <div class="activity-info">
                            <div class="activity-name">春季赏花节</div>
                            <div class="activity-time">3月15日 - 4月30日</div>
                        </div>
                    </div>
                    <div class="activity-card" onclick="showActivityDetail('环湖骑行大赛')">
                        <div class="activity-image">
                            <div class="activity-status">即将开始</div>
                        </div>
                        <div class="activity-info">
                            <div class="activity-name">环湖骑行大赛</div>
                            <div class="activity-time">5月1日 - 5月3日</div>
                        </div>
                    </div>
                    <div class="activity-card" onclick="showActivityDetail('古镇文化节')">
                        <div class="activity-image">
                            <div class="activity-status">进行中</div>
                        </div>
                        <div class="activity-info">
                            <div class="activity-name">古镇文化节</div>
                            <div class="activity-time">4月10日 - 4月20日</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 游玩路线推荐 -->
            <div class="routes">
                <div class="section-title">🚗 游玩路线推荐</div>
                <div class="route-list">
                    <div class="route-card" onclick="showRouteDetail('亲子游')">
                        <div class="route-header">
                            <div class="route-name">两天亲子游路线</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">动物园 → 科技馆 → 融创乐园，适合家庭出游</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('文化游')">
                        <div class="route-header">
                            <div class="route-name">三天文化游路线</div>
                            <div class="route-duration">3天</div>
                        </div>
                        <div class="route-desc">三河古镇 → 三国遗址公园 → 渡江战役纪念馆 → 包公园</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('生态游')">
                        <div class="route-header">
                            <div class="route-name">两天生态休闲路线</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">环巢湖风景区，享受湖光山色，放松身心</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('骆岗游')">
                        <div class="route-header">
                            <div class="route-name">一天骆岗旅游路线</div>
                            <div class="route-duration">1天</div>
                        </div>
                        <div class="route-desc">骆岗公园深度游，体验城市绿肺魅力</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('康养游')">
                        <div class="route-header">
                            <div class="route-name">两天康养度假路线</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">庐江山水片区，康养度假，山水怡人</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 探索页面 -->
        <div id="explore-page" class="page-content">
            <div class="header">
                <h1>探索合肥</h1>
                <div class="subtitle">发现更多精彩</div>
            </div>

            <div style="padding: 20px 16px;">
                <div class="map-filters">
                    <button class="filter-btn active" onclick="filterExplore('spots')">景点</button>
                    <button class="filter-btn" onclick="filterExplore('food')">美食</button>
                    <button class="filter-btn" onclick="filterExplore('hotels')">酒店</button>
                    <button class="filter-btn" onclick="filterExplore('guides')">攻略</button>
                    <button class="filter-btn" onclick="filterExplore('blogs')">游记</button>
                </div>

                <div id="explore-content">
                    <!-- 景点内容 -->
                    <div class="explore-section" id="spots-section">
                        <div class="route-list">
                            <div class="route-card">
                                <div class="route-header">
                                    <div class="route-name">骆岗公园</div>
                                    <div class="route-duration">免费</div>
                                </div>
                                <div class="route-desc">距离: 2.5km | 游玩时间: 2-3小时 | 评分: 4.6⭐</div>
                            </div>
                            <div class="route-card">
                                <div class="route-header">
                                    <div class="route-name">三河古镇</div>
                                    <div class="route-duration">¥80</div>
                                </div>
                                <div class="route-desc">距离: 45km | 游玩时间: 半天 | 评分: 4.5⭐</div>
                            </div>
                            <div class="route-card">
                                <div class="route-header">
                                    <div class="route-name">合肥科技馆</div>
                                    <div class="route-duration">¥30</div>
                                </div>
                                <div class="route-desc">距离: 8km | 游玩时间: 3-4小时 | 评分: 4.7⭐</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 伴游助手页面 -->
        <div id="assistant-page" class="page-content">
            <div class="header">
                <h1>伴游助手</h1>
                <div class="subtitle">智能问答，贴心服务</div>
            </div>

            <div style="padding: 20px 16px;">
                <div class="section-title">🤖 常见问题</div>
                <div class="route-list">
                    <div class="route-card" onclick="askQuestion('骆岗公园怎么去？')">
                        <div class="route-name">骆岗公园怎么去？</div>
                    </div>
                    <div class="route-card" onclick="askQuestion('合肥有什么特色美食？')">
                        <div class="route-name">合肥有什么特色美食？</div>
                    </div>
                    <div class="route-card" onclick="askQuestion('环巢湖最佳游玩时间？')">
                        <div class="route-name">环巢湖最佳游玩时间？</div>
                    </div>
                </div>

                <div class="section-title" style="margin-top: 20px;">📍 选择景点咨询</div>
                <div class="map-filters">
                    <button class="filter-btn active" onclick="selectSpot('骆岗公园')">骆岗公园</button>
                    <button class="filter-btn" onclick="selectSpot('三河古镇')">三河古镇</button>
                    <button class="filter-btn" onclick="selectSpot('环巢湖')">环巢湖</button>
                    <button class="filter-btn" onclick="selectSpot('科技馆')">科技馆</button>
                </div>

                <div style="margin-top: 20px;">
                    <textarea placeholder="请输入您的问题..." style="width: 100%; height: 100px; padding: 12px; border: 1px solid #ddd; border-radius: 8px; resize: none;"></textarea>
                    <button style="width: 100%; margin-top: 12px; padding: 12px; background: #2196f3; color: white; border: none; border-radius: 8px; font-size: 16px;" onclick="submitQuestion()">提交问题</button>
                </div>
            </div>
        </div>

        <!-- 行程页面 -->
        <div id="itinerary-page" class="page-content">
            <div class="header">
                <h1>我的行程</h1>
                <div class="subtitle">规划与管理</div>
            </div>

            <div style="padding: 20px 16px;">
                <div class="services-grid" style="margin-bottom: 20px;">
                    <div class="service-card" onclick="openRouteModal()">
                        <div class="service-icon">➕</div>
                        <div class="service-name">行程规划</div>
                    </div>
                    <div class="service-card" onclick="showMyItineraries()">
                        <div class="service-icon">📋</div>
                        <div class="service-name">行程管理</div>
                    </div>
                </div>

                <div class="section-title">📅 我的行程</div>
                <div class="route-list" id="my-itineraries">
                    <div class="route-card" onclick="toggleItinerary('itinerary1')">
                        <div class="route-header">
                            <div class="route-name">春游合肥三日行</div>
                            <div class="route-duration">3天</div>
                        </div>
                        <div class="route-desc">4月15日 - 4月17日</div>
                        <div id="itinerary1" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #eee;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                <strong>Day 1:</strong> 骆岗公园 (9:00-12:00) → 午餐 (12:00-13:00) → 科技馆 (14:00-17:00)
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                <strong>Day 2:</strong> 三河古镇 (9:00-17:00)
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                <strong>Day 3:</strong> 环巢湖 (9:00-16:00)
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button style="flex: 1; padding: 6px; background: #4caf50; color: white; border: none; border-radius: 4px; font-size: 12px;">修改</button>
                                <button style="flex: 1; padding: 6px; background: #f44336; color: white; border: none; border-radius: 4px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="route-card" onclick="toggleItinerary('itinerary2')">
                        <div class="route-header">
                            <div class="route-name">周末亲子游</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">4月22日 - 4月23日</div>
                        <div id="itinerary2" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #eee;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                <strong>Day 1:</strong> 动物园 (9:00-15:00) → 融创乐园 (16:00-20:00)
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                <strong>Day 2:</strong> 科技馆 (9:00-12:00) → 骆岗公园 (14:00-17:00)
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button style="flex: 1; padding: 6px; background: #4caf50; color: white; border: none; border-radius: 4px; font-size: 12px;">修改</button>
                                <button style="flex: 1; padding: 6px; background: #f44336; color: white; border: none; border-radius: 4px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的页面 -->
        <div id="profile-page" class="page-content">
            <div class="header">
                <h1>我的</h1>
                <div class="subtitle">个人中心</div>
            </div>

            <div style="padding: 20px 16px;">
                <!-- 个人信息 -->
                <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #2196f3, #4caf50); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; margin-right: 16px;">👤</div>
                        <div>
                            <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">旅行者</div>
                            <div style="font-size: 14px; color: #666;">合肥文旅爱好者</div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-around; text-align: center;">
                        <div>
                            <div style="font-size: 20px; font-weight: 600; color: #2196f3;">5</div>
                            <div style="font-size: 12px; color: #666;">游记数</div>
                        </div>
                        <div>
                            <div style="font-size: 20px; font-weight: 600; color: #4caf50;">1280</div>
                            <div style="font-size: 12px; color: #666;">积分</div>
                        </div>
                        <div>
                            <div style="font-size: 20px; font-weight: 600; color: #ff9800;">12</div>
                            <div style="font-size: 12px; color: #666;">足迹</div>
                        </div>
                    </div>
                </div>

                <!-- 我的游记 -->
                <div class="section-title">📝 我的游记</div>
                <div class="route-list">
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">春日骆岗公园漫步记</div>
                            <div style="font-size: 12px; color: #666;">3月28日</div>
                        </div>
                        <div class="route-desc">阳春三月，骆岗公园樱花盛开...</div>
                    </div>
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">三河古镇一日游攻略</div>
                            <div style="font-size: 12px; color: #666;">3月15日</div>
                        </div>
                        <div class="route-desc">千年古镇的魅力，值得细细品味...</div>
                    </div>
                </div>

                <!-- 我的订单 -->
                <div class="section-title" style="margin-top: 20px;">🎫 我的订单</div>
                <div class="route-list">
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">骆岗无人巴士票</div>
                            <div style="font-size: 12px; color: #4caf50;">已支付</div>
                        </div>
                        <div class="route-desc">订单号: HF202403280001 | 使用日期: 4月15日</div>
                        <button style="margin-top: 8px; padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 4px; font-size: 12px;" onclick="showQRCode()">查看二维码</button>
                    </div>
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">三河古镇门票</div>
                            <div style="font-size: 12px; color: #ff9800;">待使用</div>
                        </div>
                        <div class="route-desc">订单号: HF202403250002 | 使用日期: 4月22日</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showPage('home')">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item" onclick="showPage('explore')">
                <div class="nav-icon">🔍</div>
                <div class="nav-label">探索</div>
            </div>
            <div class="nav-item" onclick="showPage('assistant')">
                <div class="nav-icon">🤖</div>
                <div class="nav-label">伴游助手</div>
            </div>
            <div class="nav-item" onclick="showPage('itinerary')">
                <div class="nav-icon">📅</div>
                <div class="nav-label">行程</div>
            </div>
            <div class="nav-item" onclick="showPage('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </div>
        </div>

        <!-- 模态框 -->
        <!-- 路径规划全屏模态框 -->
        <div id="route-modal" class="route-modal">
            <div class="route-modal-content">
                <!-- 顶部导航 -->
                <div class="route-header">
                    <button class="route-back-btn" onclick="closeRouteModal()">&larr;</button>
                    <div class="route-title">路径规划</div>
                    <div></div>
                </div>

                <!-- 地图区域 -->
                <div class="route-map-container" id="route-map">
                    <!-- 路线点将通过JavaScript动态添加 -->
                </div>

                <!-- 标签页 -->
                <div class="route-tabs">
                    <button class="route-tab active" onclick="switchRouteTab('recommend')">推荐路线</button>
                    <button class="route-tab" onclick="switchRouteTab('ai')">AI规划</button>
                </div>

                <!-- 推荐路线内容 -->
                <div id="recommend-routes" class="route-content">
                    <div class="route-list-item" onclick="selectRecommendRoute('family', '两日亲子游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">两日亲子游</div>
                                <div style="font-size: 12px; color: #666;">动物园 → 科技馆 → 融创乐园</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥380</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('culture', '三天文化游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">三天文化游</div>
                                <div style="font-size: 12px; color: #666;">三河古镇 → 包公园 → 渡江战役纪念馆</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥520</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('eco', '两天生态休闲游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">两天生态休闲游</div>
                                <div style="font-size: 12px; color: #666;">环巢湖风景区 → 湿地公园</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥280</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('luogang', '一天骆岗游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">一天骆岗游</div>
                                <div style="font-size: 12px; color: #666;">骆岗公园深度体验</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">免费</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('wellness', '两天康养度假')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">两天康养度假</div>
                                <div style="font-size: 12px; color: #666;">庐江山水片区 → 温泉度假村</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥680</div>
                        </div>
                    </div>
                </div>

                <!-- AI规划内容 -->
                <div id="ai-planning" class="route-content" style="display: none;">
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">游玩时间</label>
                        <select id="ai-duration" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                            <option value="1">1天</option>
                            <option value="2">2天</option>
                            <option value="3">3天</option>
                            <option value="4">4天</option>
                            <option value="5">5天</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">人数</label>
                        <input type="number" id="ai-people" placeholder="请输入人数" min="1" max="20" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">类型</label>
                        <select id="ai-type" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                            <option value="family">亲子游</option>
                            <option value="culture">文化游</option>
                            <option value="eco">生态游</option>
                            <option value="wellness">康养游</option>
                            <option value="adventure">探险游</option>
                            <option value="food">美食游</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">预算（元）</label>
                        <input type="number" id="ai-budget" placeholder="请输入预算" min="0" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">其他需求</label>
                        <textarea id="ai-requirements" placeholder="请输入其他需求，如交通方式、特殊偏好等..." style="width: 100%; height: 80px; padding: 12px; border: 1px solid #ddd; border-radius: 8px; resize: none; font-size: 14px;"></textarea>
                    </div>
                    <button class="route-btn route-btn-primary" onclick="generateAIRoute()" style="width: 100%;">🤖 AI智能生成路线</button>
                </div>
            </div>

            <!-- 底部路线详情面板 -->
            <div id="route-bottom-panel" class="route-bottom-panel">
                <div class="route-info-header">
                    <div class="route-info-title" id="selected-route-name">路线名称</div>
                    <div class="route-info-price" id="selected-route-price">¥0</div>
                </div>

                <div class="route-spots-container">
                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">途经景点</div>
                    <div class="route-spots-scroll" id="route-spots-list">
                        <!-- 景点卡片将通过JavaScript动态添加 -->
                    </div>
                </div>

                <div class="route-actions">
                    <button class="route-btn route-btn-outline" onclick="addSpotToRoute()">➕ 添加景点</button>
                    <button class="route-btn route-btn-secondary" onclick="saveRoute()">💾 保存行程</button>
                    <button class="route-btn route-btn-primary" onclick="payForRoute()">💳 一键付费</button>
                </div>
            </div>
        </div>

        <!-- 伴游导览模态框 -->
        <div id="guide-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">伴游导览</div>
                    <button class="close-btn" onclick="closeModal('guide-modal')">&times;</button>
                </div>
                <div>
                    <div style="background: #f5f5f5; border-radius: 8px; padding: 16px; margin-bottom: 16px; text-align: center;">
                        <div style="font-size: 16px; margin-bottom: 8px;">📍 当前位置：骆岗公园</div>
                        <div style="font-size: 14px; color: #666;">附近景点：科技馆(2.5km) | 包公园(5.2km)</div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <button style="width: 100%; padding: 12px; background: #4caf50; color: white; border: none; border-radius: 8px; margin-bottom: 8px;" onclick="startVoiceGuide()">🎧 开始语音导览</button>
                        <button style="width: 100%; padding: 12px; background: #2196f3; color: white; border: none; border-radius: 8px;" onclick="findNearby()">🔍 查找附近景点</button>
                    </div>

                    <div style="font-size: 14px; color: #666; text-align: center;">
                        点击地图上的景点图标可获取详细介绍
                    </div>
                </div>
            </div>
        </div>

        <!-- 游记生成模态框 -->
        <div id="travellog-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">游记生成</div>
                    <button class="close-btn" onclick="closeModal('travellog-modal')">&times;</button>
                </div>
                <div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">游记标题</label>
                        <input type="text" placeholder="请输入游记标题" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">游玩地点</label>
                        <input type="text" placeholder="请输入游玩地点" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">游玩感受</label>
                        <textarea placeholder="请描述您的游玩感受..." style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: none;"></textarea>
                    </div>
                    <button style="width: 100%; padding: 12px; background: #4caf50; color: white; border: none; border-radius: 8px;" onclick="generateTravelLog()">✨ AI生成游记</button>
                </div>
            </div>
        </div>

        <!-- 添加景点模态框 -->
        <div id="add-spot-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">添加景点</div>
                    <button class="close-btn" onclick="closeModal('add-spot-modal')">&times;</button>
                </div>
                <div>
                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">推荐景点</div>
                        <div id="available-spots-list">
                            <!-- 景点列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">或输入自定义景点</label>
                        <input type="text" id="custom-spot-name" placeholder="请输入景点名称" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 8px;">
                        <input type="number" id="custom-spot-price" placeholder="门票价格（元）" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button style="flex: 1; padding: 12px; background: #f5f5f5; color: #333; border: none; border-radius: 8px;" onclick="closeModal('add-spot-modal')">取消</button>
                        <button style="flex: 1; padding: 12px; background: #2196f3; color: white; border: none; border-radius: 8px;" onclick="confirmAddSpot()">确认添加</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Banner轮播功能
        let currentSlide = 0;
        const slides = document.querySelectorAll('.banner-slide');
        const dots = document.querySelectorAll('.dot');

        function showSlide(index) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('active');

            currentSlide = index;

            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        // 自动轮播
        setInterval(() => {
            const nextSlide = (currentSlide + 1) % slides.length;
            showSlide(nextSlide);
        }, 4000);

        // 页面切换功能
        function showPage(pageName) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageName + '-page').classList.add('active');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.nav-item').classList.add('active');
        }

        // 路线数据
        const routeData = {
            family: {
                name: '两日亲子游',
                price: 380,
                spots: [
                    { name: '合肥野生动物园', tag: '亲子', price: 80, image: 'zoo', position: { top: '20%', left: '30%' } },
                    { name: '合肥科技馆', tag: '科技', price: 30, image: 'tech', position: { top: '40%', left: '60%' } },
                    { name: '融创乐园', tag: '娱乐', price: 270, image: 'park', position: { top: '70%', left: '40%' } }
                ]
            },
            culture: {
                name: '三天文化游',
                price: 520,
                spots: [
                    { name: '三河古镇', tag: '古镇', price: 80, image: 'town', position: { top: '30%', left: '20%' } },
                    { name: '包公园', tag: '历史', price: 20, image: 'park', position: { top: '50%', left: '50%' } },
                    { name: '渡江战役纪念馆', tag: '红色', price: 0, image: 'museum', position: { top: '60%', left: '70%' } },
                    { name: '李鸿章故居', tag: '名人', price: 20, image: 'house', position: { top: '40%', left: '80%' } }
                ]
            },
            eco: {
                name: '两天生态休闲游',
                price: 280,
                spots: [
                    { name: '环巢湖风景区', tag: '生态', price: 0, image: 'lake', position: { top: '25%', left: '25%' } },
                    { name: '湿地公园', tag: '自然', price: 0, image: 'wetland', position: { top: '45%', left: '45%' } },
                    { name: '巢湖渔家乐', tag: '美食', price: 280, image: 'food', position: { top: '65%', left: '65%' } }
                ]
            },
            luogang: {
                name: '一天骆岗游',
                price: 0,
                spots: [
                    { name: '骆岗公园', tag: '公园', price: 0, image: 'park', position: { top: '50%', left: '50%' } }
                ]
            },
            wellness: {
                name: '两天康养度假',
                price: 680,
                spots: [
                    { name: '庐江汤池温泉', tag: '温泉', price: 180, image: 'spring', position: { top: '30%', left: '70%' } },
                    { name: '万山度假村', tag: '度假', price: 500, image: 'resort', position: { top: '60%', left: '80%' } }
                ]
            }
        };

        let currentRoute = null;

        // 模态框功能
        function openRouteModal() {
            document.getElementById('route-modal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeRouteModal() {
            document.getElementById('route-modal').style.display = 'none';
            document.body.style.overflow = 'auto';
            hideRouteBottomPanel();
        }

        function openGuideModal() {
            document.getElementById('guide-modal').style.display = 'block';
        }

        function openTravelLogModal() {
            document.getElementById('travellog-modal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 地图筛选功能
        function filterMap(type) {
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log('筛选类型:', type);
        }

        // 显示景点信息
        function showSpotInfo(name, desc) {
            alert(`${name}\n\n${desc}`);
        }

        // 显示活动详情
        function showActivityDetail(activityName) {
            alert(`活动详情：${activityName}\n\n更多详情请关注官方公告`);
        }

        // 显示路线详情
        function showRouteDetail(routeType) {
            alert(`路线详情：${routeType}\n\n点击可查看详细行程安排`);
        }

        // 路径规划标签页切换
        function switchRouteTab(tab) {
            document.querySelectorAll('.route-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            if (tab === 'recommend') {
                document.getElementById('recommend-routes').style.display = 'block';
                document.getElementById('ai-planning').style.display = 'none';
            } else {
                document.getElementById('recommend-routes').style.display = 'none';
                document.getElementById('ai-planning').style.display = 'block';
            }
            hideRouteBottomPanel();
        }

        // 选择推荐路线
        function selectRecommendRoute(routeId, routeName) {
            // 移除之前的选中状态
            document.querySelectorAll('.route-list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            event.target.classList.add('selected');

            // 设置当前路线
            currentRoute = routeData[routeId];

            // 在地图上显示路线
            displayRouteOnMap(currentRoute);

            // 显示底部详情面板
            showRouteBottomPanel(currentRoute);
        }

        // 在地图上显示路线
        function displayRouteOnMap(route) {
            const mapContainer = document.getElementById('route-map');

            // 清除之前的路线点
            mapContainer.querySelectorAll('.route-point, .route-line').forEach(el => el.remove());

            // 添加路线点
            route.spots.forEach((spot, index) => {
                const point = document.createElement('div');
                point.className = 'route-point';
                point.style.top = spot.position.top;
                point.style.left = spot.position.left;
                point.textContent = index + 1;
                point.title = spot.name;
                point.onclick = () => showSpotDetail(spot);
                mapContainer.appendChild(point);

                // 添加连接线（除了最后一个点）
                if (index < route.spots.length - 1) {
                    const nextSpot = route.spots[index + 1];
                    const line = createRouteLine(spot.position, nextSpot.position);
                    mapContainer.appendChild(line);
                }
            });
        }

        // 创建路线连接线
        function createRouteLine(start, end) {
            const line = document.createElement('div');
            line.className = 'route-line';

            const startX = parseFloat(start.left);
            const startY = parseFloat(start.top);
            const endX = parseFloat(end.left);
            const endY = parseFloat(end.top);

            const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
            const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;

            line.style.width = length + '%';
            line.style.left = startX + '%';
            line.style.top = startY + '%';
            line.style.transform = `rotate(${angle}deg)`;
            line.style.transformOrigin = '0 50%';

            return line;
        }

        // 显示底部路线详情面板
        function showRouteBottomPanel(route) {
            const panel = document.getElementById('route-bottom-panel');
            const routeName = document.getElementById('selected-route-name');
            const routePrice = document.getElementById('selected-route-price');
            const spotsList = document.getElementById('route-spots-list');

            routeName.textContent = route.name;
            routePrice.textContent = route.price === 0 ? '免费' : `¥${route.price}`;

            // 清空并重新填充景点列表
            spotsList.innerHTML = '';
            route.spots.forEach((spot, index) => {
                const spotCard = createSpotCard(spot, index);
                spotsList.appendChild(spotCard);
            });

            panel.classList.add('show');
        }

        // 隐藏底部面板
        function hideRouteBottomPanel() {
            document.getElementById('route-bottom-panel').classList.remove('show');
        }

        // 创建景点卡片
        function createSpotCard(spot, index) {
            const card = document.createElement('div');
            card.className = 'route-spot-card';
            card.innerHTML = `
                <div class="route-spot-image"></div>
                <div class="route-spot-name">${spot.name}</div>
                <div class="route-spot-tag">${spot.tag}</div>
                <div class="route-spot-price">${spot.price === 0 ? '免费' : '¥' + spot.price}</div>
                <button class="route-spot-delete" onclick="removeSpotFromRoute(${index})">&times;</button>
            `;
            return card;
        }

        // 显示景点详情
        function showSpotDetail(spot) {
            alert(`${spot.name}\n\n标签：${spot.tag}\n费用：${spot.price === 0 ? '免费' : '¥' + spot.price}`);
        }

        // 从路线中移除景点
        function removeSpotFromRoute(index) {
            if (currentRoute && currentRoute.spots.length > 1) {
                const removedSpot = currentRoute.spots.splice(index, 1)[0];
                currentRoute.price -= removedSpot.price;

                // 重新显示地图和底部面板
                displayRouteOnMap(currentRoute);
                showRouteBottomPanel(currentRoute);

                alert(`已移除 ${removedSpot.name}`);
            } else {
                alert('至少需要保留一个景点');
            }
        }

        // 可选择的景点数据
        const availableSpots = [
            { name: '逍遥津公园', tag: '公园', price: 0 },
            { name: '安徽博物院', tag: '博物馆', price: 0 },
            { name: '明教寺', tag: '寺庙', price: 10 },
            { name: '合肥大剧院', tag: '文化', price: 0 },
            { name: '徽园', tag: '园林', price: 20 },
            { name: '杏花公园', tag: '公园', price: 0 },
            { name: '蜀山森林公园', tag: '自然', price: 0 },
            { name: '安徽名人馆', tag: '文化', price: 15 }
        ];

        let selectedSpotToAdd = null;

        // 添加景点到路线
        function addSpotToRoute() {
            if (!currentRoute) {
                alert('请先选择一条路线');
                return;
            }

            // 显示添加景点模态框
            showAddSpotModal();
        }

        // 显示添加景点模态框
        function showAddSpotModal() {
            const modal = document.getElementById('add-spot-modal');
            const spotsList = document.getElementById('available-spots-list');

            // 清空并重新填充景点列表
            spotsList.innerHTML = '';
            availableSpots.forEach(spot => {
                const spotItem = document.createElement('div');
                spotItem.className = 'route-list-item';
                spotItem.style.marginBottom = '8px';
                spotItem.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500; margin-bottom: 2px;">${spot.name}</div>
                            <div style="font-size: 12px; color: #666;">${spot.tag}</div>
                        </div>
                        <div style="color: #ff4444; font-weight: 500;">${spot.price === 0 ? '免费' : '¥' + spot.price}</div>
                    </div>
                `;
                spotItem.onclick = () => selectSpotToAdd(spot, spotItem);
                spotsList.appendChild(spotItem);
            });

            modal.style.display = 'block';
        }

        // 选择要添加的景点
        function selectSpotToAdd(spot, element) {
            // 移除之前的选中状态
            document.querySelectorAll('#available-spots-list .route-list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedSpotToAdd = spot;
        }

        // 确认添加景点
        function confirmAddSpot() {
            let spotToAdd = selectedSpotToAdd;

            // 如果没有选择推荐景点，检查自定义输入
            if (!spotToAdd) {
                const customName = document.getElementById('custom-spot-name').value.trim();
                const customPrice = document.getElementById('custom-spot-price').value;

                if (customName) {
                    spotToAdd = {
                        name: customName,
                        tag: '自定义',
                        price: parseInt(customPrice) || 0
                    };
                }
            }

            if (spotToAdd && currentRoute) {
                // 为新景点分配位置
                const newPosition = {
                    top: Math.random() * 60 + 20 + '%',
                    left: Math.random() * 60 + 20 + '%'
                };

                const newSpot = { ...spotToAdd, position: newPosition };
                currentRoute.spots.push(newSpot);
                currentRoute.price += newSpot.price;

                // 重新显示地图和底部面板
                displayRouteOnMap(currentRoute);
                showRouteBottomPanel(currentRoute);

                // 关闭模态框并重置
                closeModal('add-spot-modal');
                selectedSpotToAdd = null;
                document.getElementById('custom-spot-name').value = '';
                document.getElementById('custom-spot-price').value = '';

                alert(`已添加 ${spotToAdd.name} 到路线中`);
            } else {
                alert('请选择一个景点或输入自定义景点信息');
            }
        }

        // 保存路线
        function saveRoute() {
            if (currentRoute) {
                alert(`路线 "${currentRoute.name}" 已保存到我的行程\n\n您可以在"行程"页面查看和管理`);
            }
        }

        // 一键付费
        function payForRoute() {
            if (currentRoute) {
                const totalPrice = currentRoute.price;
                if (totalPrice === 0) {
                    alert('该路线为免费路线，无需付费\n\n已为您保存行程安排');
                } else {
                    alert(`确认支付 ¥${totalPrice}？\n\n支付成功后将自动保存行程并生成订单`);
                }
            }
        }

        // 生成AI路线
        function generateAIRoute() {
            const duration = document.getElementById('ai-duration').value;
            const people = document.getElementById('ai-people').value;
            const type = document.getElementById('ai-type').value;
            const budget = document.getElementById('ai-budget').value;
            const requirements = document.getElementById('ai-requirements').value;

            if (!people || !budget) {
                alert('请填写完整的规划信息');
                return;
            }

            // 模拟AI生成路线
            alert(`正在为您生成个性化路线...\n\n游玩时间：${duration}天\n人数：${people}人\n类型：${document.querySelector('#ai-type option:checked').text}\n预算：¥${budget}\n\n请稍候，AI正在分析最佳路线...`);

            // 模拟生成结果
            setTimeout(() => {
                const aiRoute = {
                    name: `AI定制${duration}日游`,
                    price: Math.min(parseInt(budget), 800),
                    spots: [
                        { name: 'AI推荐景点1', tag: '智能推荐', price: 50, position: { top: '30%', left: '40%' } },
                        { name: 'AI推荐景点2', tag: '智能推荐', price: 80, position: { top: '60%', left: '60%' } }
                    ]
                };

                currentRoute = aiRoute;
                displayRouteOnMap(aiRoute);
                showRouteBottomPanel(aiRoute);

                alert('AI路线生成完成！\n\n已根据您的需求生成最优路线，请查看地图和详情');
            }, 2000);
        }

        // 探索页面筛选
        function filterExplore(type) {
            document.querySelectorAll('#explore-page .filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的内容切换逻辑
            console.log('探索筛选:', type);
        }

        // 伴游助手功能
        function askQuestion(question) {
            alert(`您的问题：${question}\n\n正在为您查询相关信息...`);
        }

        function selectSpot(spotName) {
            document.querySelectorAll('#assistant-page .filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            alert(`已选择景点：${spotName}\n\n您可以询问关于该景点的任何问题`);
        }

        function submitQuestion() {
            const textarea = document.querySelector('#assistant-page textarea');
            const question = textarea.value.trim();
            if (question) {
                alert(`您的问题：${question}\n\n正在为您查询答案...`);
                textarea.value = '';
            } else {
                alert('请输入您的问题');
            }
        }

        // 行程管理功能
        function toggleItinerary(itineraryId) {
            const element = document.getElementById(itineraryId);
            if (element.style.display === 'none') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }

        function showMyItineraries() {
            alert('显示我的所有行程');
        }

        // 伴游导览功能
        function startVoiceGuide() {
            alert('🎧 语音导览已开启\n\n正在为您播放当前位置的详细介绍...');
        }

        function findNearby() {
            alert('🔍 正在搜索附近景点...\n\n发现3个附近景点，请查看地图标记');
        }

        // 游记生成功能
        function generateTravelLog() {
            alert('✨ AI正在为您生成游记...\n\n请稍候，精彩的游记即将完成');
        }

        // 显示二维码
        function showQRCode() {
            alert('📱 订单二维码\n\n请在景区入口扫描此二维码');
        }
    </script>
</body>
</html>
