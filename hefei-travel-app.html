<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 顶部导航 */
        .header {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            padding: 20px 16px 16px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .header .subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 4px;
        }
        
        /* Banner轮播 */
        .banner-container {
            position: relative;
            height: 180px;
            overflow: hidden;
        }
        
        .banner-slide {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .banner-slide.active {
            opacity: 1;
        }
        
        .banner-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .banner-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.6));
            color: white;
            padding: 20px 16px 16px;
        }
        
        .banner-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .banner-desc {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .banner-dots {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 6px;
        }
        
        .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .dot.active {
            background: white;
        }
        
        /* 智能服务 */
        .smart-services {
            padding: 20px 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2196f3;
        }
        
        .services-grid {
            display: flex;
            gap: 12px;
        }
        
        .service-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .service-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 8px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .service-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        /* 合肥旅游圈 */
        .tourism-circle {
            padding: 0 16px 20px;
        }
        
        .map-container {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .map-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-btn {
            padding: 6px 12px;
            border-radius: 16px;
            background: #f5f5f5;
            border: none;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-btn.active {
            background: #2196f3;
            color: white;
        }
        
        .map-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .map-point {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #ff4444;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }
        
        .point-1 { top: 30%; left: 40%; }
        .point-2 { top: 60%; left: 70%; }
        .point-3 { top: 45%; left: 20%; }
        .point-4 { top: 70%; left: 45%; }
        
        /* 推荐活动 */
        .activities {
            padding: 0 16px 20px;
        }
        
        .activity-list {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .activity-card {
            min-width: 200px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .activity-image {
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, #4caf50, #2196f3);
            position: relative;
        }
        
        .activity-status {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4444;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
        }
        
        .activity-info {
            padding: 12px;
        }
        
        .activity-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #666;
        }
        
        /* 游玩路线推荐 */
        .routes {
            padding: 0 16px 20px;
        }
        
        .route-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .route-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .route-card:hover {
            transform: translateY(-2px);
        }
        
        .route-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .route-name {
            font-size: 16px;
            font-weight: 600;
            color: #2196f3;
        }
        
        .route-duration {
            font-size: 12px;
            color: #666;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 8px;
        }
        
        .route-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            padding: 8px 0;
            z-index: 1000;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: #2196f3;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        /* 页面内容 */
        .page-content {
            display: none;
            padding: 20px 16px 80px;
        }
        
        .page-content.active {
            display: block;
        }
        
        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 20px;
            width: 90%;
            max-width: 350px;
            max-height: 80vh;
            overflow-y: auto;
        }

        /* 路径规划专用全屏模态框 */
        .route-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 2000;
            overflow-y: auto;
        }

        .route-modal-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .route-header {
            /* background: linear-gradient(135deg, #2196f3, #4caf50);*/
            color: white;
            padding: 20px 16px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .route-back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .route-title {
            font-size: 18px;
            font-weight: 600;
        }

        .route-map-container {
            position: relative;
            height: 300px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            background-image:
                radial-gradient(circle at 30% 40%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="%23ddd" stroke-width="1" opacity="0.3"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/><path d="M50 150 Q100 130 150 150 T250 170 Q300 180 350 160" stroke="%234caf50" stroke-width="3" fill="none" opacity="0.6"/><path d="M80 200 Q130 220 180 200 T280 210" stroke="%232196f3" stroke-width="3" fill="none" opacity="0.6"/><circle cx="120" cy="100" r="6" fill="%23ff6b6b" opacity="0.8"/><circle cx="220" cy="180" r="6" fill="%23ff6b6b" opacity="0.8"/><circle cx="320" cy="140" r="6" fill="%23ff6b6b" opacity="0.8"/><text x="130" y="105" font-size="8" fill="%23666">骆岗公园</text><text x="230" y="185" font-size="8" fill="%23666">科技馆</text><text x="330" y="145" font-size="8" fill="%23666">三河古镇</text><text x="20" y="30" font-size="12" fill="%234caf50" font-weight="bold">合肥市手绘地图</text></svg>');
            background-size: cover;
            background-position: center;
            border-bottom: 1px solid #eee;
        }

        .route-point {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff4444;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .route-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #2196f3, #4caf50);
            border-radius: 2px;
        }

        .route-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #eee;
        }

        .route-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: none;
            border: none;
            font-size: 14px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }

        .route-tab.active {
            color: #2196f3;
            border-bottom-color: #2196f3;
        }

        .route-content {
            padding: 16px;
        }

        .route-list-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .route-list-item:hover {
            transform: translateY(-1px);
        }

        .route-list-item.selected {
            border: 2px solid #2196f3;
            background: #f3f9ff;
        }

        .route-bottom-panel {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #eee;
            padding: 16px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            max-height: 50vh;
            overflow-y: auto;
        }

        .route-bottom-panel.show {
            transform: translateY(0);
        }

        .route-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .route-info-title {
            font-size: 18px;
            font-weight: 600;
            color: #2196f3;
        }

        .route-info-price {
            font-size: 16px;
            font-weight: 600;
            color: #ff4444;
        }

        .route-spots-container {
            margin-bottom: 16px;
        }

        .route-spots-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .route-spot-card {
            min-width: 120px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 8px;
            text-align: center;
            position: relative;
        }

        .route-spot-image {
            width: 100%;
            height: 60px;
            background: linear-gradient(135deg, #4caf50, #2196f3);
            border-radius: 4px;
            margin-bottom: 6px;
            position: relative;
        }

        .route-spot-name {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .route-spot-tag {
            font-size: 10px;
            color: #666;
            background: #e0e0e0;
            padding: 2px 6px;
            border-radius: 8px;
            margin-bottom: 2px;
        }

        .route-spot-price {
            font-size: 11px;
            color: #ff4444;
            font-weight: 500;
        }

        .route-spot-delete {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 16px;
            height: 16px;
            background: #f44336;
            color: white;
            border-radius: 50%;
            border: none;
            font-size: 10px;
            cursor: pointer;
            display: none;
        }

        .route-spot-card:hover .route-spot-delete {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .route-actions {
            display: flex;
            gap: 8px;
        }

        .route-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }

        .route-btn-primary {
            background: #2196f3;
            color: white;
        }

        .route-btn-primary:hover {
            background: #1976d2;
        }

        .route-btn-secondary {
            background: #4caf50;
            color: white;
        }

        .route-btn-secondary:hover {
            background: #388e3c;
        }

        .route-btn-outline {
            background: white;
            color: #2196f3;
            border: 1px solid #2196f3;
        }

        .route-btn-outline:hover {
            background: #f3f9ff;
        }

        /* 伴游导览专用样式 */
        .guide-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 2000;
            overflow-y: auto;
        }

        .guide-map-container {
            position: relative;
            height: 350px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            background-image:
                radial-gradient(circle at 30% 40%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 350"><defs><pattern id="guide-grid" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M 30 0 L 0 0 0 30" fill="none" stroke="%23ddd" stroke-width="1" opacity="0.2"/></pattern></defs><rect width="100%" height="100%" fill="url(%23guide-grid)"/><path d="M50 175 Q100 155 150 175 T250 195 Q300 205 350 185" stroke="%234caf50" stroke-width="2" fill="none" opacity="0.4"/><path d="M80 225 Q130 245 180 225 T280 235" stroke="%232196f3" stroke-width="2" fill="none" opacity="0.4"/><text x="20" y="30" font-size="14" fill="%234caf50" font-weight="bold">合肥导览地图</text><text x="20" y="50" font-size="10" fill="%23666">当前位置：骆岗公园</text></svg>');
            background-size: cover;
            background-position: center;
            border-bottom: 1px solid #eee;
        }

        .guide-spot {
            position: absolute;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 3px solid white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .guide-spot:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
        }

        .guide-spot.current-location {
            background: #ff4444;
            animation: currentPulse 2s infinite;
        }

        .guide-spot.nearby {
            background: #4caf50;
        }

        .guide-spot.visited {
            background: #2196f3;
            animation: visitedBlink 1s infinite;
        }

        .guide-spot.selected {
            background: #ff9800;
            animation: selectedGlow 1.5s infinite;
        }

        @keyframes currentPulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 15px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }

        @keyframes visitedBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.6; }
        }

        @keyframes selectedGlow {
            0%, 100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7); }
            50% { box-shadow: 0 0 0 10px rgba(255, 152, 0, 0); }
        }

        .guide-controls {
            padding: 16px;
            background: white;
            border-bottom: 1px solid #eee;
        }

        .guide-status {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            text-align: center;
        }

        .guide-status-title {
            font-size: 16px;
            font-weight: 600;
            color: #2196f3;
            margin-bottom: 4px;
        }

        .guide-status-desc {
            font-size: 12px;
            color: #666;
        }

        .guide-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .guide-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .guide-btn-primary {
            background: #2196f3;
            color: white;
        }

        .guide-btn-secondary {
            background: #4caf50;
            color: white;
        }

        .guide-btn-voice {
            background: #ff9800;
            color: white;
        }

        .guide-btn.active {
            transform: scale(0.95);
        }

        .voice-input-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            display: none;
        }

        .voice-input-container.active {
            display: block;
        }

        .voice-status {
            text-align: center;
            margin-bottom: 12px;
        }

        .voice-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #ff4444;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: voicePulse 1.5s infinite;
        }

        @keyframes voicePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .route-suggestion {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: none;
        }

        .route-suggestion.show {
            display: block;
        }

        .route-suggestion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .route-suggestion-title {
            font-size: 14px;
            font-weight: 600;
            color: #2196f3;
        }

        .route-suggestion-distance {
            font-size: 12px;
            color: #666;
        }

        .transport-options {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .transport-option {
            flex: 1;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .transport-option:hover {
            background: #f0f0f0;
        }

        .transport-option.selected {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .audio-player {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            display: none;
        }

        .audio-player.show {
            display: block;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .audio-play-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #2196f3;
            color: white;
            border: none;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .audio-info {
            flex: 1;
        }

        .audio-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .audio-desc {
            font-size: 12px;
            color: #666;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }
        
        /* 响应式 */
        @media (max-width: 375px) {
            .services-grid {
                gap: 8px;
            }
            
            .service-card {
                padding: 12px 8px;
            }
            
            .service-icon {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
            
            .service-name {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 首页内容 -->
        <div id="home-page" class="page-content active">
            <!-- 顶部导航 -->
            <div class="header">
                <h1>合肥文旅助手</h1>
                <div class="subtitle">打造一站式旅游服务与体验平台</div>
            </div>
            
            <!-- Banner轮播 -->
            <div class="banner-container">
                <div class="banner-slide active">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop" alt="骆岗公园">
                    <div class="banner-overlay">
                        <div class="banner-title">骆岗公园</div>
                        <div class="banner-desc">文化集中区 · 城市绿肺</div>
                    </div>
                </div>
                <div class="banner-slide">
                    <img src="https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800&h=400&fit=crop" alt="环巢湖片区">
                    <div class="banner-overlay">
                        <div class="banner-title">环巢湖片区</div>
                        <div class="banner-desc">生态休闲 · 湖光山色</div>
                    </div>
                </div>
                <div class="banner-slide">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop" alt="庐江山水片区">
                    <div class="banner-overlay">
                        <div class="banner-title">庐江山水片区</div>
                        <div class="banner-desc">康养度假 · 山水怡人</div>
                    </div>
                </div>
                <div class="banner-dots">
                    <div class="dot active" onclick="showSlide(0)"></div>
                    <div class="dot" onclick="showSlide(1)"></div>
                    <div class="dot" onclick="showSlide(2)"></div>
                </div>
            </div>

            <!-- 智能服务 -->
            <div class="smart-services">
                <div class="section-title">🤖 智能服务</div>
                <div class="services-grid">
                    <div class="service-card" onclick="openRouteModal()">
                        <div class="service-icon">🗺️</div>
                        <div class="service-name">路径规划</div>
                    </div>
                    <div class="service-card" onclick="openGuideModal()">
                        <div class="service-icon">🎧</div>
                        <div class="service-name">伴游导览</div>
                    </div>
                    <div class="service-card" onclick="openTravelLogModal()">
                        <div class="service-icon">📝</div>
                        <div class="service-name">游记生成</div>
                    </div>
                </div>
            </div>

            <!-- 合肥旅游圈 -->
            <div class="tourism-circle">
                <div class="section-title">🌟 合肥旅游圈</div>
                <div class="map-container">
                    <div class="map-filters">
                        <button class="filter-btn active" onclick="filterMap('all')">全部</button>
                        <button class="filter-btn" onclick="filterMap('scenic')">特色旅游</button>
                        <button class="filter-btn" onclick="filterMap('culture')">历史文化</button>
                        <button class="filter-btn" onclick="filterMap('family')">亲子科技</button>
                    </div>
                    <div class="map-image">
                        <div class="map-point point-1" onclick="showSpotInfo('骆岗公园', '文化集中区，城市绿肺，适合休闲漫步')"></div>
                        <div class="map-point point-2" onclick="showSpotInfo('环巢湖', '生态休闲胜地，湖光山色美不胜收')"></div>
                        <div class="map-point point-3" onclick="showSpotInfo('三河古镇', '千年古镇，历史文化底蕴深厚')"></div>
                        <div class="map-point point-4" onclick="showSpotInfo('合肥科技馆', '科技体验，亲子互动的好去处')"></div>
                    </div>
                </div>
            </div>

            <!-- 推荐活动 -->
            <div class="activities">
                <div class="section-title">🎉 推荐活动</div>
                <div class="activity-list">
                    <div class="activity-card" onclick="showActivityDetail('春季赏花节')">
                        <div class="activity-image">
                            <div class="activity-status">进行中</div>
                        </div>
                        <div class="activity-info">
                            <div class="activity-name">春季赏花节</div>
                            <div class="activity-time">3月15日 - 4月30日</div>
                        </div>
                    </div>
                    <div class="activity-card" onclick="showActivityDetail('环湖骑行大赛')">
                        <div class="activity-image">
                            <div class="activity-status">即将开始</div>
                        </div>
                        <div class="activity-info">
                            <div class="activity-name">环湖骑行大赛</div>
                            <div class="activity-time">5月1日 - 5月3日</div>
                        </div>
                    </div>
                    <div class="activity-card" onclick="showActivityDetail('古镇文化节')">
                        <div class="activity-image">
                            <div class="activity-status">进行中</div>
                        </div>
                        <div class="activity-info">
                            <div class="activity-name">古镇文化节</div>
                            <div class="activity-time">4月10日 - 4月20日</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 游玩路线推荐 -->
            <div class="routes">
                <div class="section-title">🚗 游玩路线推荐</div>
                <div class="route-list">
                    <div class="route-card" onclick="showRouteDetail('亲子游')">
                        <div class="route-header">
                            <div class="route-name">两天亲子游路线</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">动物园 → 科技馆 → 融创乐园，适合家庭出游</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('文化游')">
                        <div class="route-header">
                            <div class="route-name">三天文化游路线</div>
                            <div class="route-duration">3天</div>
                        </div>
                        <div class="route-desc">三河古镇 → 三国遗址公园 → 渡江战役纪念馆 → 包公园</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('生态游')">
                        <div class="route-header">
                            <div class="route-name">两天生态休闲路线</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">环巢湖风景区，享受湖光山色，放松身心</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('骆岗游')">
                        <div class="route-header">
                            <div class="route-name">一天骆岗旅游路线</div>
                            <div class="route-duration">1天</div>
                        </div>
                        <div class="route-desc">骆岗公园深度游，体验城市绿肺魅力</div>
                    </div>
                    <div class="route-card" onclick="showRouteDetail('康养游')">
                        <div class="route-header">
                            <div class="route-name">两天康养度假路线</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">庐江山水片区，康养度假，山水怡人</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 探索页面 -->
        <div id="explore-page" class="page-content">
            <div class="header">
                <h1>探索合肥</h1>
                <div class="subtitle">发现更多精彩</div>
            </div>

            <div style="padding: 20px 16px;">
                <div class="map-filters">
                    <button class="filter-btn active" onclick="filterExplore('spots')">景点</button>
                    <button class="filter-btn" onclick="filterExplore('food')">美食</button>
                    <button class="filter-btn" onclick="filterExplore('hotels')">酒店</button>
                    <button class="filter-btn" onclick="filterExplore('guides')">攻略</button>
                    <button class="filter-btn" onclick="filterExplore('blogs')">游记</button>
                </div>

                <div id="explore-content">
                    <!-- 景点内容 -->
                    <div class="explore-section" id="spots-section">
                        <div class="route-list">
                            <div class="route-card">
                                <div class="route-header">
                                    <div class="route-name">骆岗公园</div>
                                    <div class="route-duration">免费</div>
                                </div>
                                <div class="route-desc">距离: 2.5km | 游玩时间: 2-3小时 | 评分: 4.6⭐</div>
                            </div>
                            <div class="route-card">
                                <div class="route-header">
                                    <div class="route-name">三河古镇</div>
                                    <div class="route-duration">¥80</div>
                                </div>
                                <div class="route-desc">距离: 45km | 游玩时间: 半天 | 评分: 4.5⭐</div>
                            </div>
                            <div class="route-card">
                                <div class="route-header">
                                    <div class="route-name">合肥科技馆</div>
                                    <div class="route-duration">¥30</div>
                                </div>
                                <div class="route-desc">距离: 8km | 游玩时间: 3-4小时 | 评分: 4.7⭐</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 伴游助手页面 -->
        <div id="assistant-page" class="page-content">
            <div class="header">
                <h1>伴游助手</h1>
                <div class="subtitle">智能问答，贴心服务</div>
            </div>

            <div style="padding: 20px 16px;">
                <div class="section-title">🤖 常见问题</div>
                <div class="route-list">
                    <div class="route-card" onclick="askQuestion('骆岗公园怎么去？')">
                        <div class="route-name">骆岗公园怎么去？</div>
                    </div>
                    <div class="route-card" onclick="askQuestion('合肥有什么特色美食？')">
                        <div class="route-name">合肥有什么特色美食？</div>
                    </div>
                    <div class="route-card" onclick="askQuestion('环巢湖最佳游玩时间？')">
                        <div class="route-name">环巢湖最佳游玩时间？</div>
                    </div>
                </div>

                <div class="section-title" style="margin-top: 20px;">📍 选择景点咨询</div>
                <div class="map-filters">
                    <button class="filter-btn active" onclick="selectSpot('骆岗公园')">骆岗公园</button>
                    <button class="filter-btn" onclick="selectSpot('三河古镇')">三河古镇</button>
                    <button class="filter-btn" onclick="selectSpot('环巢湖')">环巢湖</button>
                    <button class="filter-btn" onclick="selectSpot('科技馆')">科技馆</button>
                </div>

                <div style="margin-top: 20px;">
                    <textarea placeholder="请输入您的问题..." style="width: 100%; height: 100px; padding: 12px; border: 1px solid #ddd; border-radius: 8px; resize: none;"></textarea>
                    <button style="width: 100%; margin-top: 12px; padding: 12px; background: #2196f3; color: white; border: none; border-radius: 8px; font-size: 16px;" onclick="submitQuestion()">提交问题</button>
                </div>
            </div>
        </div>

        <!-- 行程页面 -->
        <div id="itinerary-page" class="page-content">
            <div class="header">
                <h1>我的行程</h1>
                <div class="subtitle">规划与管理</div>
            </div>

            <div style="padding: 20px 16px;">
                <div class="services-grid" style="margin-bottom: 20px;">
                    <div class="service-card" onclick="openRouteModal()">
                        <div class="service-icon">➕</div>
                        <div class="service-name">行程规划</div>
                    </div>
                    <div class="service-card" onclick="showMyItineraries()">
                        <div class="service-icon">📋</div>
                        <div class="service-name">行程管理</div>
                    </div>
                </div>

                <div class="section-title">📅 我的行程</div>
                <div class="route-list" id="my-itineraries">
                    <div class="route-card" onclick="toggleItinerary('itinerary1')">
                        <div class="route-header">
                            <div class="route-name">春游合肥三日行</div>
                            <div class="route-duration">3天</div>
                        </div>
                        <div class="route-desc">4月15日 - 4月17日</div>
                        <div id="itinerary1" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #eee;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                <strong>Day 1:</strong> 骆岗公园 (9:00-12:00) → 午餐 (12:00-13:00) → 科技馆 (14:00-17:00)
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                <strong>Day 2:</strong> 三河古镇 (9:00-17:00)
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                <strong>Day 3:</strong> 环巢湖 (9:00-16:00)
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button style="flex: 1; padding: 6px; background: #4caf50; color: white; border: none; border-radius: 4px; font-size: 12px;">修改</button>
                                <button style="flex: 1; padding: 6px; background: #f44336; color: white; border: none; border-radius: 4px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="route-card" onclick="toggleItinerary('itinerary2')">
                        <div class="route-header">
                            <div class="route-name">周末亲子游</div>
                            <div class="route-duration">2天</div>
                        </div>
                        <div class="route-desc">4月22日 - 4月23日</div>
                        <div id="itinerary2" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #eee;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                <strong>Day 1:</strong> 动物园 (9:00-15:00) → 融创乐园 (16:00-20:00)
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                <strong>Day 2:</strong> 科技馆 (9:00-12:00) → 骆岗公园 (14:00-17:00)
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button style="flex: 1; padding: 6px; background: #4caf50; color: white; border: none; border-radius: 4px; font-size: 12px;">修改</button>
                                <button style="flex: 1; padding: 6px; background: #f44336; color: white; border: none; border-radius: 4px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的页面 -->
        <div id="profile-page" class="page-content">
            <div class="header">
                <h1>我的</h1>
                <div class="subtitle">个人中心</div>
            </div>

            <div style="padding: 20px 16px;">
                <!-- 个人信息 -->
                <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #2196f3, #4caf50); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; margin-right: 16px;">👤</div>
                        <div>
                            <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">旅行者</div>
                            <div style="font-size: 14px; color: #666;">合肥文旅爱好者</div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-around; text-align: center;">
                        <div>
                            <div style="font-size: 20px; font-weight: 600; color: #2196f3;">5</div>
                            <div style="font-size: 12px; color: #666;">游记数</div>
                        </div>
                        <div>
                            <div style="font-size: 20px; font-weight: 600; color: #4caf50;">1280</div>
                            <div style="font-size: 12px; color: #666;">积分</div>
                        </div>
                        <div>
                            <div style="font-size: 20px; font-weight: 600; color: #ff9800;">12</div>
                            <div style="font-size: 12px; color: #666;">足迹</div>
                        </div>
                    </div>
                </div>

                <!-- 我的游记 -->
                <div class="section-title">📝 我的游记</div>
                <div class="route-list">
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">春日骆岗公园漫步记</div>
                            <div style="font-size: 12px; color: #666;">3月28日</div>
                        </div>
                        <div class="route-desc">阳春三月，骆岗公园樱花盛开...</div>
                    </div>
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">三河古镇一日游攻略</div>
                            <div style="font-size: 12px; color: #666;">3月15日</div>
                        </div>
                        <div class="route-desc">千年古镇的魅力，值得细细品味...</div>
                    </div>
                </div>

                <!-- 我的订单 -->
                <div class="section-title" style="margin-top: 20px;">🎫 我的订单</div>
                <div class="route-list">
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">骆岗无人巴士票</div>
                            <div style="font-size: 12px; color: #4caf50;">已支付</div>
                        </div>
                        <div class="route-desc">订单号: HF202403280001 | 使用日期: 4月15日</div>
                        <button style="margin-top: 8px; padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 4px; font-size: 12px;" onclick="showQRCode()">查看二维码</button>
                    </div>
                    <div class="route-card">
                        <div class="route-header">
                            <div class="route-name">三河古镇门票</div>
                            <div style="font-size: 12px; color: #ff9800;">待使用</div>
                        </div>
                        <div class="route-desc">订单号: HF202403250002 | 使用日期: 4月22日</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showPage('home')">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item" onclick="showPage('explore')">
                <div class="nav-icon">🔍</div>
                <div class="nav-label">探索</div>
            </div>
            <div class="nav-item" onclick="showPage('assistant')">
                <div class="nav-icon">🤖</div>
                <div class="nav-label">伴游助手</div>
            </div>
            <div class="nav-item" onclick="showPage('itinerary')">
                <div class="nav-icon">📅</div>
                <div class="nav-label">行程</div>
            </div>
            <div class="nav-item" onclick="showPage('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </div>
        </div>

        <!-- 模态框 -->
        <!-- 路径规划全屏模态框 -->
        <div id="route-modal" class="route-modal">
            <div class="route-modal-content">
                <!-- 顶部导航 -->
                <div class="route-header">
                    <button class="route-back-btn" onclick="closeRouteModal()">&larr;</button>
                    <div class="route-title">路径规划</div>
                    <div></div>
                </div>

                <!-- 地图区域 -->
                <div class="route-map-container" id="route-map">
                    <!-- 路线点将通过JavaScript动态添加 -->
                </div>

                <!-- 标签页 -->
                <div class="route-tabs">
                    <button class="route-tab active" onclick="switchRouteTab('recommend')">推荐路线</button>
                    <button class="route-tab" onclick="switchRouteTab('ai')">AI规划</button>
                </div>

                <!-- 推荐路线内容 -->
                <div id="recommend-routes" class="route-content">
                    <div class="route-list-item" onclick="selectRecommendRoute('family', '两日亲子游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">两日亲子游</div>
                                <div style="font-size: 12px; color: #666;">动物园 → 科技馆 → 融创乐园</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥380</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('culture', '三天文化游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">三天文化游</div>
                                <div style="font-size: 12px; color: #666;">三河古镇 → 包公园 → 渡江战役纪念馆</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥520</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('eco', '两天生态休闲游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">两天生态休闲游</div>
                                <div style="font-size: 12px; color: #666;">环巢湖风景区 → 湿地公园</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥280</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('luogang', '一天骆岗游')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">一天骆岗游</div>
                                <div style="font-size: 12px; color: #666;">骆岗公园深度体验</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">免费</div>
                        </div>
                    </div>

                    <div class="route-list-item" onclick="selectRecommendRoute('wellness', '两天康养度假')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">两天康养度假</div>
                                <div style="font-size: 12px; color: #666;">庐江山水片区 → 温泉度假村</div>
                            </div>
                            <div style="color: #ff4444; font-weight: 600;">¥680</div>
                        </div>
                    </div>
                </div>

                <!-- AI规划内容 -->
                <div id="ai-planning" class="route-content" style="display: none;">
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">游玩时间</label>
                        <select id="ai-duration" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                            <option value="1">1天</option>
                            <option value="2">2天</option>
                            <option value="3">3天</option>
                            <option value="4">4天</option>
                            <option value="5">5天</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">人数</label>
                        <input type="number" id="ai-people" placeholder="请输入人数" min="1" max="20" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">类型</label>
                        <select id="ai-type" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                            <option value="family">亲子游</option>
                            <option value="culture">文化游</option>
                            <option value="eco">生态游</option>
                            <option value="wellness">康养游</option>
                            <option value="adventure">探险游</option>
                            <option value="food">美食游</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">预算（元）</label>
                        <input type="number" id="ai-budget" placeholder="请输入预算" min="0" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px; font-weight: 500;">其他需求</label>
                        <textarea id="ai-requirements" placeholder="请输入其他需求，如交通方式、特殊偏好等..." style="width: 100%; height: 80px; padding: 12px; border: 1px solid #ddd; border-radius: 8px; resize: none; font-size: 14px;"></textarea>
                    </div>
                    <button class="route-btn route-btn-primary" onclick="generateAIRoute()" style="width: 100%;">🤖 AI智能生成路线</button>
                </div>
            </div>

            <!-- 底部路线详情面板 -->
            <div id="route-bottom-panel" class="route-bottom-panel">
                <div class="route-info-header">
                    <div class="route-info-title" id="selected-route-name">路线名称</div>
                    <div class="route-info-price" id="selected-route-price">¥0</div>
                </div>

                <div class="route-spots-container">
                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">途经景点</div>
                    <div class="route-spots-scroll" id="route-spots-list">
                        <!-- 景点卡片将通过JavaScript动态添加 -->
                    </div>
                </div>

                <div class="route-actions">
                    <button class="route-btn route-btn-outline" onclick="addSpotToRoute()">➕ 添加景点</button>
                    <button class="route-btn route-btn-secondary" onclick="saveRoute()">💾 保存行程</button>
                    <button class="route-btn route-btn-primary" onclick="payForRoute()">💳 一键付费</button>
                </div>
            </div>
        </div>

        <!-- 伴游导览全屏模态框 -->
        <div id="guide-modal" class="guide-modal">
            <div class="route-modal-content">
                <!-- 顶部导航 -->
                <div class="route-header">
                    <button class="route-back-btn" onclick="closeGuideModal()">&larr;</button>
                    <div class="route-title">伴游导览</div>
                    <button class="route-back-btn" onclick="toggleGuideSettings()">⚙️</button>
                </div>

                <!-- 地图区域 -->
                <div class="guide-map-container" id="guide-map">
                    <!-- 景点将通过JavaScript动态添加 -->
                </div>

                <!-- 控制面板 -->
                <div class="guide-controls">
                    <!-- 当前状态 -->
                    <div class="guide-status">
                        <div class="guide-status-title" id="current-location-text">📍 当前位置：骆岗公园</div>
                        <div class="guide-status-desc" id="nearby-spots-text">附近景点：科技馆(2.5km) | 包公园(5.2km) | 三河古镇(45km)</div>
                    </div>

                    <!-- 功能按钮 -->
                    <div class="guide-buttons">
                        <button class="guide-btn guide-btn-primary" onclick="findNearbySpots()">🔍 附近景点</button>
                        <button class="guide-btn guide-btn-secondary" onclick="startAutoGuide()">🎧 自动导览</button>
                        <button class="guide-btn guide-btn-voice" onclick="toggleVoiceInput()">🎤 语音助手</button>
                    </div>

                    <!-- 语音输入区域 -->
                    <div id="voice-input-container" class="voice-input-container">
                        <div class="voice-status">
                            <div class="voice-indicator">🎤</div>
                            <div id="voice-status-text">点击开始语音识别</div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="guide-btn guide-btn-voice" onclick="startVoiceRecognition()" style="flex: 1;">开始录音</button>
                            <button class="guide-btn guide-btn-primary" onclick="stopVoiceRecognition()" style="flex: 1;">停止录音</button>
                        </div>
                        <div style="margin-top: 8px;">
                            <input type="text" id="voice-input-text" placeholder="或直接输入您的问题..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>

                    <!-- 路线建议 -->
                    <div id="route-suggestions-container">
                        <!-- 路线建议将通过JavaScript动态添加 -->
                    </div>
                </div>

                <!-- 音频播放器 -->
                <div id="audio-player" class="audio-player">
                    <div class="audio-controls">
                        <button class="audio-play-btn" id="audio-play-btn" onclick="toggleAudioPlayback()">▶️</button>
                        <div class="audio-info">
                            <div class="audio-title" id="audio-title">景点介绍</div>
                            <div class="audio-desc" id="audio-desc">正在播放语音导览...</div>
                        </div>
                        <button class="guide-btn guide-btn-primary" onclick="closeAudioPlayer()" style="padding: 6px 12px;">关闭</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游记生成模态框 -->
        <div id="travellog-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">游记生成</div>
                    <button class="close-btn" onclick="closeModal('travellog-modal')">&times;</button>
                </div>
                <div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">游记标题</label>
                        <input type="text" placeholder="请输入游记标题" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">游玩地点</label>
                        <input type="text" placeholder="请输入游玩地点" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">游玩感受</label>
                        <textarea placeholder="请描述您的游玩感受..." style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: none;"></textarea>
                    </div>
                    <button style="width: 100%; padding: 12px; background: #4caf50; color: white; border: none; border-radius: 8px;" onclick="generateTravelLog()">✨ AI生成游记</button>
                </div>
            </div>
        </div>

        <!-- 添加景点模态框 -->
        <div id="add-spot-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">添加景点</div>
                    <button class="close-btn" onclick="closeModal('add-spot-modal')">&times;</button>
                </div>
                <div>
                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">推荐景点</div>
                        <div id="available-spots-list">
                            <!-- 景点列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 14px;">或输入自定义景点</label>
                        <input type="text" id="custom-spot-name" placeholder="请输入景点名称" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 8px;">
                        <input type="number" id="custom-spot-price" placeholder="门票价格（元）" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button style="flex: 1; padding: 12px; background: #f5f5f5; color: #333; border: none; border-radius: 8px;" onclick="closeModal('add-spot-modal')">取消</button>
                        <button style="flex: 1; padding: 12px; background: #2196f3; color: white; border: none; border-radius: 8px;" onclick="confirmAddSpot()">确认添加</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Banner轮播功能
        let currentSlide = 0;
        const slides = document.querySelectorAll('.banner-slide');
        const dots = document.querySelectorAll('.dot');

        function showSlide(index) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('active');

            currentSlide = index;

            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        // 自动轮播
        setInterval(() => {
            const nextSlide = (currentSlide + 1) % slides.length;
            showSlide(nextSlide);
        }, 4000);

        // 页面切换功能
        function showPage(pageName) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageName + '-page').classList.add('active');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.nav-item').classList.add('active');

            // 如果切换到其他页面，停止音频播放
            if (pageName !== 'assistant') {
                stopAudioPlayback();
            }
        }

        // 路线数据
        const routeData = {
            family: {
                name: '两日亲子游',
                price: 380,
                spots: [
                    { name: '合肥野生动物园', tag: '亲子', price: 80, image: 'zoo', position: { top: '20%', left: '30%' } },
                    { name: '合肥科技馆', tag: '科技', price: 30, image: 'tech', position: { top: '40%', left: '60%' } },
                    { name: '融创乐园', tag: '娱乐', price: 270, image: 'park', position: { top: '70%', left: '40%' } }
                ]
            },
            culture: {
                name: '三天文化游',
                price: 520,
                spots: [
                    { name: '三河古镇', tag: '古镇', price: 80, image: 'town', position: { top: '30%', left: '20%' } },
                    { name: '包公园', tag: '历史', price: 20, image: 'park', position: { top: '50%', left: '50%' } },
                    { name: '渡江战役纪念馆', tag: '红色', price: 0, image: 'museum', position: { top: '60%', left: '70%' } },
                    { name: '李鸿章故居', tag: '名人', price: 20, image: 'house', position: { top: '40%', left: '80%' } }
                ]
            },
            eco: {
                name: '两天生态休闲游',
                price: 280,
                spots: [
                    { name: '环巢湖风景区', tag: '生态', price: 0, image: 'lake', position: { top: '25%', left: '25%' } },
                    { name: '湿地公园', tag: '自然', price: 0, image: 'wetland', position: { top: '45%', left: '45%' } },
                    { name: '巢湖渔家乐', tag: '美食', price: 280, image: 'food', position: { top: '65%', left: '65%' } }
                ]
            },
            luogang: {
                name: '一天骆岗游',
                price: 0,
                spots: [
                    { name: '骆岗公园', tag: '公园', price: 0, image: 'park', position: { top: '50%', left: '50%' } }
                ]
            },
            wellness: {
                name: '两天康养度假',
                price: 680,
                spots: [
                    { name: '庐江汤池温泉', tag: '温泉', price: 180, image: 'spring', position: { top: '30%', left: '70%' } },
                    { name: '万山度假村', tag: '度假', price: 500, image: 'resort', position: { top: '60%', left: '80%' } }
                ]
            }
        };

        let currentRoute = null;

        // 模态框功能
        function openRouteModal() {
            document.getElementById('route-modal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeRouteModal() {
            document.getElementById('route-modal').style.display = 'none';
            document.body.style.overflow = 'auto';
            hideRouteBottomPanel();
        }

        // 伴游导览数据
        const guideSpots = {
            'luogang-park': {
                name: '骆岗公园',
                type: 'current',
                position: { top: '50%', left: '50%' },
                description: '合肥最大的城市公园，占地15.5平方公里，被誉为"城市绿肺"。',
                audio: '欢迎来到骆岗公园！这里是合肥最大的城市公园，拥有丰富的生态资源和休闲设施...',
                distance: 0,
                facilities: ['停车场', '卫生间', '餐厅', '儿童游乐区']
            },
            'tech-museum': {
                name: '合肥科技馆',
                type: 'nearby',
                position: { top: '30%', left: '70%' },
                description: '集科普教育、科技展示、科学体验于一体的现代化科技馆。',
                audio: '合肥科技馆是一座现代化的科普教育基地，这里有丰富的科技展品和互动体验...',
                distance: 2.5,
                facilities: ['停车场', '卫生间', '咖啡厅', '纪念品店']
            },
            'baogong-park': {
                name: '包公园',
                type: 'nearby',
                position: { top: '70%', left: '30%' },
                description: '为纪念北宋名臣包拯而建的主题公园，展现包公文化。',
                audio: '包公园是为了纪念北宋名臣包拯而建造的，这里展示了包公的生平事迹...',
                distance: 5.2,
                facilities: ['停车场', '卫生间', '茶室', '文化展厅']
            },
            'sanhe-town': {
                name: '三河古镇',
                type: 'nearby',
                position: { top: '20%', left: '20%' },
                description: '有着2500多年历史的古镇，保存完好的明清建筑群。',
                audio: '三河古镇有着2500多年的悠久历史，这里保存着完好的明清建筑群...',
                distance: 45,
                facilities: ['停车场', '卫生间', '餐厅', '民宿', '游船码头']
            },
            'xiaoyao-park': {
                name: '逍遥津公园',
                type: 'nearby',
                position: { top: '40%', left: '80%' },
                description: '合肥市内著名的综合性公园，有着深厚的历史文化底蕴。',
                audio: '逍遥津公园是合肥市内的著名公园，这里有着深厚的历史文化底蕴...',
                distance: 8.5,
                facilities: ['停车场', '卫生间', '游乐设施', '湖心亭']
            }
        };

        let currentSelectedSpot = null;
        let isVoiceInputActive = false;
        let isAudioPlaying = false;
        let currentAudio = null;

        function openGuideModal() {
            document.getElementById('guide-modal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            initializeGuideMap();
            updateLocationStatus();
        }

        function closeGuideModal() {
            document.getElementById('guide-modal').style.display = 'none';
            document.body.style.overflow = 'auto';
            stopAudioPlayback();
            hideVoiceInput();
        }

        // 初始化导览地图
        function initializeGuideMap() {
            const mapContainer = document.getElementById('guide-map');

            // 清除之前的景点
            mapContainer.querySelectorAll('.guide-spot').forEach(el => el.remove());

            // 添加所有景点
            Object.keys(guideSpots).forEach(spotId => {
                const spot = guideSpots[spotId];
                const spotElement = createGuideSpot(spotId, spot);
                mapContainer.appendChild(spotElement);
            });
        }

        // 创建导览景点元素
        function createGuideSpot(spotId, spot) {
            const element = document.createElement('div');
            element.className = `guide-spot ${spot.type}`;
            element.style.top = spot.position.top;
            element.style.left = spot.position.left;
            element.textContent = spot.name.charAt(0);
            element.title = spot.name;
            element.onclick = () => selectGuideSpot(spotId, spot);
            return element;
        }

        // 选择导览景点
        function selectGuideSpot(spotId, spot) {
            // 移除之前的选中状态
            document.querySelectorAll('.guide-spot').forEach(el => {
                el.classList.remove('selected');
            });

            // 添加选中状态
            event.target.classList.add('selected');
            currentSelectedSpot = { id: spotId, data: spot };

            // 如果用户进入景点（距离为0），则闪烁并自动播放介绍
            if (spot.distance === 0) {
                event.target.classList.add('visited');
                setTimeout(() => {
                    startSpotAudioGuide(spot);
                }, 500);
            } else {
                // 显示路线建议
                showRouteSuggestions(spot);
            }
        }

        // 开始景点语音导览
        function startSpotAudioGuide(spot) {
            const audioPlayer = document.getElementById('audio-player');
            const audioTitle = document.getElementById('audio-title');
            const audioDesc = document.getElementById('audio-desc');
            const playBtn = document.getElementById('audio-play-btn');

            audioTitle.textContent = spot.name;
            audioDesc.textContent = spot.description;
            audioPlayer.classList.add('show');

            // 模拟语音播放
            playBtn.textContent = '⏸️';
            isAudioPlaying = true;

            // 使用Web Speech API进行语音播放（如果支持）
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(spot.audio);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                utterance.onend = () => {
                    playBtn.textContent = '▶️';
                    isAudioPlaying = false;
                };
                speechSynthesis.speak(utterance);
                currentAudio = utterance;
            }
        }

        // 更新位置状态
        function updateLocationStatus() {
            const locationText = document.getElementById('current-location-text');
            const nearbyText = document.getElementById('nearby-spots-text');

            // 模拟位置更新
            const nearbySpots = Object.values(guideSpots)
                .filter(spot => spot.distance > 0 && spot.distance < 10)
                .sort((a, b) => a.distance - b.distance)
                .slice(0, 3);

            const nearbyList = nearbySpots.map(spot => `${spot.name}(${spot.distance}km)`).join(' | ');
            nearbyText.textContent = `附近景点：${nearbyList}`;
        }

        function openTravelLogModal() {
            document.getElementById('travellog-modal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 地图筛选功能
        function filterMap(type) {
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log('筛选类型:', type);
        }

        // 显示景点信息
        function showSpotInfo(name, desc) {
            alert(`${name}\n\n${desc}`);
        }

        // 显示活动详情
        function showActivityDetail(activityName) {
            alert(`活动详情：${activityName}\n\n更多详情请关注官方公告`);
        }

        // 显示路线详情
        function showRouteDetail(routeType) {
            alert(`路线详情：${routeType}\n\n点击可查看详细行程安排`);
        }

        // 路径规划标签页切换
        function switchRouteTab(tab) {
            document.querySelectorAll('.route-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            if (tab === 'recommend') {
                document.getElementById('recommend-routes').style.display = 'block';
                document.getElementById('ai-planning').style.display = 'none';
            } else {
                document.getElementById('recommend-routes').style.display = 'none';
                document.getElementById('ai-planning').style.display = 'block';
            }
            hideRouteBottomPanel();
        }

        // 选择推荐路线
        function selectRecommendRoute(routeId, routeName) {
            // 移除之前的选中状态
            document.querySelectorAll('.route-list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            event.target.classList.add('selected');

            // 设置当前路线
            currentRoute = routeData[routeId];

            // 在地图上显示路线
            displayRouteOnMap(currentRoute);

            // 显示底部详情面板
            showRouteBottomPanel(currentRoute);
        }

        // 在地图上显示路线
        function displayRouteOnMap(route) {
            const mapContainer = document.getElementById('route-map');

            // 清除之前的路线点
            mapContainer.querySelectorAll('.route-point, .route-line').forEach(el => el.remove());

            // 添加路线点
            route.spots.forEach((spot, index) => {
                const point = document.createElement('div');
                point.className = 'route-point';
                point.style.top = spot.position.top;
                point.style.left = spot.position.left;
                point.textContent = index + 1;
                point.title = spot.name;
                point.onclick = () => showSpotDetail(spot);
                mapContainer.appendChild(point);

                // 添加连接线（除了最后一个点）
                if (index < route.spots.length - 1) {
                    const nextSpot = route.spots[index + 1];
                    const line = createRouteLine(spot.position, nextSpot.position);
                    mapContainer.appendChild(line);
                }
            });
        }

        // 创建路线连接线
        function createRouteLine(start, end) {
            const line = document.createElement('div');
            line.className = 'route-line';

            const startX = parseFloat(start.left);
            const startY = parseFloat(start.top);
            const endX = parseFloat(end.left);
            const endY = parseFloat(end.top);

            const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
            const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;

            line.style.width = length + '%';
            line.style.left = startX + '%';
            line.style.top = startY + '%';
            line.style.transform = `rotate(${angle}deg)`;
            line.style.transformOrigin = '0 50%';

            return line;
        }

        // 显示底部路线详情面板
        function showRouteBottomPanel(route) {
            const panel = document.getElementById('route-bottom-panel');
            const routeName = document.getElementById('selected-route-name');
            const routePrice = document.getElementById('selected-route-price');
            const spotsList = document.getElementById('route-spots-list');

            routeName.textContent = route.name;
            routePrice.textContent = route.price === 0 ? '免费' : `¥${route.price}`;

            // 清空并重新填充景点列表
            spotsList.innerHTML = '';
            route.spots.forEach((spot, index) => {
                const spotCard = createSpotCard(spot, index);
                spotsList.appendChild(spotCard);
            });

            panel.classList.add('show');
        }

        // 隐藏底部面板
        function hideRouteBottomPanel() {
            document.getElementById('route-bottom-panel').classList.remove('show');
        }

        // 创建景点卡片
        function createSpotCard(spot, index) {
            const card = document.createElement('div');
            card.className = 'route-spot-card';
            card.innerHTML = `
                <div class="route-spot-image"></div>
                <div class="route-spot-name">${spot.name}</div>
                <div class="route-spot-tag">${spot.tag}</div>
                <div class="route-spot-price">${spot.price === 0 ? '免费' : '¥' + spot.price}</div>
                <button class="route-spot-delete" onclick="removeSpotFromRoute(${index})">&times;</button>
            `;
            return card;
        }

        // 显示景点详情
        function showSpotDetail(spot) {
            alert(`${spot.name}\n\n标签：${spot.tag}\n费用：${spot.price === 0 ? '免费' : '¥' + spot.price}`);
        }

        // 从路线中移除景点
        function removeSpotFromRoute(index) {
            if (currentRoute && currentRoute.spots.length > 1) {
                const removedSpot = currentRoute.spots.splice(index, 1)[0];
                currentRoute.price -= removedSpot.price;

                // 重新显示地图和底部面板
                displayRouteOnMap(currentRoute);
                showRouteBottomPanel(currentRoute);

                alert(`已移除 ${removedSpot.name}`);
            } else {
                alert('至少需要保留一个景点');
            }
        }

        // 可选择的景点数据
        const availableSpots = [
            { name: '逍遥津公园', tag: '公园', price: 0 },
            { name: '安徽博物院', tag: '博物馆', price: 0 },
            { name: '明教寺', tag: '寺庙', price: 10 },
            { name: '合肥大剧院', tag: '文化', price: 0 },
            { name: '徽园', tag: '园林', price: 20 },
            { name: '杏花公园', tag: '公园', price: 0 },
            { name: '蜀山森林公园', tag: '自然', price: 0 },
            { name: '安徽名人馆', tag: '文化', price: 15 }
        ];

        let selectedSpotToAdd = null;

        // 添加景点到路线
        function addSpotToRoute() {
            if (!currentRoute) {
                alert('请先选择一条路线');
                return;
            }

            // 显示添加景点模态框
            showAddSpotModal();
        }

        // 显示添加景点模态框
        function showAddSpotModal() {
            const modal = document.getElementById('add-spot-modal');
            const spotsList = document.getElementById('available-spots-list');

            // 清空并重新填充景点列表
            spotsList.innerHTML = '';
            availableSpots.forEach(spot => {
                const spotItem = document.createElement('div');
                spotItem.className = 'route-list-item';
                spotItem.style.marginBottom = '8px';
                spotItem.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500; margin-bottom: 2px;">${spot.name}</div>
                            <div style="font-size: 12px; color: #666;">${spot.tag}</div>
                        </div>
                        <div style="color: #ff4444; font-weight: 500;">${spot.price === 0 ? '免费' : '¥' + spot.price}</div>
                    </div>
                `;
                spotItem.onclick = () => selectSpotToAdd(spot, spotItem);
                spotsList.appendChild(spotItem);
            });

            modal.style.display = 'block';
        }

        // 选择要添加的景点
        function selectSpotToAdd(spot, element) {
            // 移除之前的选中状态
            document.querySelectorAll('#available-spots-list .route-list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedSpotToAdd = spot;
        }

        // 确认添加景点
        function confirmAddSpot() {
            let spotToAdd = selectedSpotToAdd;

            // 如果没有选择推荐景点，检查自定义输入
            if (!spotToAdd) {
                const customName = document.getElementById('custom-spot-name').value.trim();
                const customPrice = document.getElementById('custom-spot-price').value;

                if (customName) {
                    spotToAdd = {
                        name: customName,
                        tag: '自定义',
                        price: parseInt(customPrice) || 0
                    };
                }
            }

            if (spotToAdd && currentRoute) {
                // 为新景点分配位置
                const newPosition = {
                    top: Math.random() * 60 + 20 + '%',
                    left: Math.random() * 60 + 20 + '%'
                };

                const newSpot = { ...spotToAdd, position: newPosition };
                currentRoute.spots.push(newSpot);
                currentRoute.price += newSpot.price;

                // 重新显示地图和底部面板
                displayRouteOnMap(currentRoute);
                showRouteBottomPanel(currentRoute);

                // 关闭模态框并重置
                closeModal('add-spot-modal');
                selectedSpotToAdd = null;
                document.getElementById('custom-spot-name').value = '';
                document.getElementById('custom-spot-price').value = '';

                alert(`已添加 ${spotToAdd.name} 到路线中`);
            } else {
                alert('请选择一个景点或输入自定义景点信息');
            }
        }

        // 保存路线
        function saveRoute() {
            if (currentRoute) {
                alert(`路线 "${currentRoute.name}" 已保存到我的行程\n\n您可以在"行程"页面查看和管理`);
            }
        }

        // 一键付费
        function payForRoute() {
            if (currentRoute) {
                const totalPrice = currentRoute.price;
                if (totalPrice === 0) {
                    alert('该路线为免费路线，无需付费\n\n已为您保存行程安排');
                } else {
                    alert(`确认支付 ¥${totalPrice}？\n\n支付成功后将自动保存行程并生成订单`);
                }
            }
        }

        // 生成AI路线
        function generateAIRoute() {
            const duration = document.getElementById('ai-duration').value;
            const people = document.getElementById('ai-people').value;
            const type = document.getElementById('ai-type').value;
            const budget = document.getElementById('ai-budget').value;
            const requirements = document.getElementById('ai-requirements').value;

            if (!people || !budget) {
                alert('请填写完整的规划信息');
                return;
            }

            // 模拟AI生成路线
            alert(`正在为您生成个性化路线...\n\n游玩时间：${duration}天\n人数：${people}人\n类型：${document.querySelector('#ai-type option:checked').text}\n预算：¥${budget}\n\n请稍候，AI正在分析最佳路线...`);

            // 模拟生成结果
            setTimeout(() => {
                const aiRoute = {
                    name: `AI定制${duration}日游`,
                    price: Math.min(parseInt(budget), 800),
                    spots: [
                        { name: 'AI推荐景点1', tag: '智能推荐', price: 50, position: { top: '30%', left: '40%' } },
                        { name: 'AI推荐景点2', tag: '智能推荐', price: 80, position: { top: '60%', left: '60%' } }
                    ]
                };

                currentRoute = aiRoute;
                displayRouteOnMap(aiRoute);
                showRouteBottomPanel(aiRoute);

                alert('AI路线生成完成！\n\n已根据您的需求生成最优路线，请查看地图和详情');
            }, 2000);
        }

        // 探索页面筛选
        function filterExplore(type) {
            document.querySelectorAll('#explore-page .filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的内容切换逻辑
            console.log('探索筛选:', type);
        }

        // 伴游助手功能
        function askQuestion(question) {
            alert(`您的问题：${question}\n\n正在为您查询相关信息...`);
        }

        function selectSpot(spotName) {
            document.querySelectorAll('#assistant-page .filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            alert(`已选择景点：${spotName}\n\n您可以询问关于该景点的任何问题`);
        }

        function submitQuestion() {
            const textarea = document.querySelector('#assistant-page textarea');
            const question = textarea.value.trim();
            if (question) {
                alert(`您的问题：${question}\n\n正在为您查询答案...`);
                textarea.value = '';
            } else {
                alert('请输入您的问题');
            }
        }

        // 行程管理功能
        function toggleItinerary(itineraryId) {
            const element = document.getElementById(itineraryId);
            if (element.style.display === 'none') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }

        function showMyItineraries() {
            alert('显示我的所有行程');
        }

        // 查找附近景点
        function findNearbySpots() {
            const nearbySpots = Object.values(guideSpots)
                .filter(spot => spot.distance > 0 && spot.distance < 20)
                .sort((a, b) => a.distance - b.distance);

            // 高亮附近景点
            document.querySelectorAll('.guide-spot').forEach(el => {
                el.classList.remove('nearby');
            });

            nearbySpots.forEach(spot => {
                const spotElement = document.querySelector(`[title="${spot.name}"]`);
                if (spotElement) {
                    spotElement.classList.add('nearby');
                }
            });

            alert(`🔍 发现 ${nearbySpots.length} 个附近景点\n\n${nearbySpots.map(s => `${s.name} (${s.distance}km)`).join('\n')}\n\n绿色图标为附近景点，点击查看详情`);
        }

        // 开始自动导览
        function startAutoGuide() {
            const currentSpot = guideSpots['luogang-park'];
            startSpotAudioGuide(currentSpot);
            alert('🎧 自动导览已开启\n\n正在为您播放当前位置的详细介绍...');
        }

        // 切换语音输入
        function toggleVoiceInput() {
            const container = document.getElementById('voice-input-container');
            isVoiceInputActive = !isVoiceInputActive;

            if (isVoiceInputActive) {
                container.classList.add('active');
            } else {
                container.classList.remove('active');
            }
        }

        function hideVoiceInput() {
            document.getElementById('voice-input-container').classList.remove('active');
            isVoiceInputActive = false;
        }

        // 语音识别功能
        function startVoiceRecognition() {
            const statusText = document.getElementById('voice-status-text');
            statusText.textContent = '正在听取您的问题...';

            // 使用Web Speech API进行语音识别（如果支持）
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();

                recognition.lang = 'zh-CN';
                recognition.continuous = false;
                recognition.interimResults = false;

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('voice-input-text').value = transcript;
                    statusText.textContent = `识别结果：${transcript}`;
                    processVoiceQuery(transcript);
                };

                recognition.onerror = function(event) {
                    statusText.textContent = '语音识别失败，请重试';
                };

                recognition.start();
            } else {
                statusText.textContent = '您的浏览器不支持语音识别';
            }
        }

        function stopVoiceRecognition() {
            const statusText = document.getElementById('voice-status-text');
            statusText.textContent = '点击开始语音识别';

            // 处理文本输入
            const inputText = document.getElementById('voice-input-text').value.trim();
            if (inputText) {
                processVoiceQuery(inputText);
            }
        }

        // 处理语音查询
        function processVoiceQuery(query) {
            const lowerQuery = query.toLowerCase();
            let response = '';
            let suggestedSpots = [];

            // 分析查询内容
            if (lowerQuery.includes('附近') || lowerQuery.includes('周围')) {
                if (lowerQuery.includes('餐厅') || lowerQuery.includes('吃饭')) {
                    response = '为您推荐附近的餐厅';
                    suggestedSpots = [
                        { name: '骆岗公园餐厅', distance: 0.2, type: '餐厅' },
                        { name: '湖心亭茶餐厅', distance: 0.5, type: '茶餐厅' }
                    ];
                } else if (lowerQuery.includes('卫生间') || lowerQuery.includes('厕所')) {
                    response = '为您指引最近的卫生间';
                    suggestedSpots = [
                        { name: '公园主入口卫生间', distance: 0.1, type: '卫生间' },
                        { name: '湖边卫生间', distance: 0.3, type: '卫生间' }
                    ];
                } else if (lowerQuery.includes('停车')) {
                    response = '为您推荐附近的停车场';
                    suggestedSpots = [
                        { name: '骆岗公园主停车场', distance: 0.2, type: '停车场' },
                        { name: '东门停车场', distance: 0.4, type: '停车场' }
                    ];
                } else {
                    response = '为您推荐附近的景点';
                    suggestedSpots = Object.values(guideSpots)
                        .filter(spot => spot.distance > 0 && spot.distance < 10)
                        .map(spot => ({ name: spot.name, distance: spot.distance, type: '景点' }));
                }
            } else if (lowerQuery.includes('怎么去') || lowerQuery.includes('路线')) {
                const spotNames = Object.values(guideSpots).map(s => s.name);
                const targetSpot = spotNames.find(name => lowerQuery.includes(name));

                if (targetSpot) {
                    const spot = Object.values(guideSpots).find(s => s.name === targetSpot);
                    showRouteSuggestions(spot);
                    response = `为您规划到${targetSpot}的路线`;
                } else {
                    response = '请告诉我您想去的具体地点';
                }
            } else {
                response = '我可以帮您查找附近的景点、餐厅、卫生间、停车场，或为您规划路线';
            }

            // 显示查询结果
            if (suggestedSpots.length > 0) {
                showVoiceQueryResults(response, suggestedSpots);
            } else {
                alert(`🤖 智能助手回复：\n\n${response}`);
            }
        }

        // 显示语音查询结果
        function showVoiceQueryResults(response, spots) {
            const spotsText = spots.map(spot => `${spot.name} (${spot.distance}km) - ${spot.type}`).join('\n');
            alert(`🤖 智能助手回复：\n\n${response}\n\n${spotsText}\n\n点击地图上的图标获取详细信息`);
        }

        // 显示路线建议
        function showRouteSuggestions(targetSpot) {
            const container = document.getElementById('route-suggestions-container');
            container.innerHTML = '';

            const suggestion = document.createElement('div');
            suggestion.className = 'route-suggestion show';

            suggestion.innerHTML = `
                <div class="route-suggestion-header">
                    <div class="route-suggestion-title">到 ${targetSpot.name}</div>
                    <div class="route-suggestion-distance">${targetSpot.distance}km</div>
                </div>
                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">${targetSpot.description}</div>
                <div class="transport-options">
                    <div class="transport-option" onclick="selectTransport('walk', '${targetSpot.name}')">
                        🚶 步行<br><span style="font-size: 9px;">${Math.round(targetSpot.distance * 12)}分钟</span>
                    </div>
                    <div class="transport-option" onclick="selectTransport('bike', '${targetSpot.name}')">
                        🚲 骑行<br><span style="font-size: 9px;">${Math.round(targetSpot.distance * 4)}分钟</span>
                    </div>
                    <div class="transport-option" onclick="selectTransport('bus', '${targetSpot.name}')">
                        🚌 公交<br><span style="font-size: 9px;">${Math.round(targetSpot.distance * 3)}分钟</span>
                    </div>
                    <div class="transport-option" onclick="selectTransport('car', '${targetSpot.name}')">
                        🚗 开车<br><span style="font-size: 9px;">${Math.round(targetSpot.distance * 2)}分钟</span>
                    </div>
                </div>
            `;

            container.appendChild(suggestion);
        }

        // 选择交通方式
        function selectTransport(transport, spotName) {
            // 移除之前的选中状态
            document.querySelectorAll('.transport-option').forEach(el => {
                el.classList.remove('selected');
            });

            // 添加选中状态
            event.target.classList.add('selected');

            const transportNames = {
                walk: '步行',
                bike: '骑行',
                bus: '公交',
                car: '开车'
            };

            const routes = {
                walk: '沿园区主路直行 → 经过湖心亭 → 到达目的地',
                bike: '骑行专用道 → 环湖路线 → 到达目的地',
                bus: '步行至公交站 → 乘坐X路公交 → 在XX站下车',
                car: '驾车出园区 → 上XX路 → 到达目的地停车场'
            };

            alert(`🗺️ ${transportNames[transport]}路线规划\n\n目的地：${spotName}\n路线：${routes[transport]}\n\n是否开始导航？`);
        }

        // 音频播放控制
        function toggleAudioPlayback() {
            const playBtn = document.getElementById('audio-play-btn');

            if (isAudioPlaying) {
                // 停止播放
                if (currentAudio && 'speechSynthesis' in window) {
                    speechSynthesis.cancel();
                }
                playBtn.textContent = '▶️';
                isAudioPlaying = false;
            } else {
                // 开始播放
                if (currentSelectedSpot) {
                    startSpotAudioGuide(currentSelectedSpot.data);
                }
            }
        }

        function stopAudioPlayback() {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();
            }
            isAudioPlaying = false;
            const playBtn = document.getElementById('audio-play-btn');
            if (playBtn) {
                playBtn.textContent = '▶️';
            }
        }

        function closeAudioPlayer() {
            stopAudioPlayback();
            document.getElementById('audio-player').classList.remove('show');
        }

        // 导览设置
        function toggleGuideSettings() {
            alert('⚙️ 导览设置\n\n• 语音播放速度\n• 自动播放开关\n• 语言选择\n• 音量调节\n\n功能开发中...');
        }

        // 模拟位置更新（用于演示）
        function simulateLocationUpdate() {
            const spots = Object.keys(guideSpots);
            const randomSpot = spots[Math.floor(Math.random() * spots.length)];
            const spot = guideSpots[randomSpot];

            // 更新当前位置
            document.getElementById('current-location-text').textContent = `📍 当前位置：${spot.name}`;

            // 如果进入新景点，自动触发介绍
            if (spot.distance === 0) {
                const spotElement = document.querySelector(`[title="${spot.name}"]`);
                if (spotElement) {
                    spotElement.classList.add('visited');
                    setTimeout(() => {
                        startSpotAudioGuide(spot);
                    }, 1000);
                }
            }

            updateLocationStatus();
        }

        // 初始化位置监听（如果支持地理位置API）
        function initializeLocationTracking() {
            if ('geolocation' in navigator) {
                navigator.geolocation.watchPosition(
                    function(position) {
                        // 在实际应用中，这里会根据GPS坐标判断用户位置
                        console.log('位置更新:', position.coords.latitude, position.coords.longitude);
                        // 可以根据坐标计算与各景点的距离
                    },
                    function(error) {
                        console.log('位置获取失败:', error.message);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化位置跟踪
            initializeLocationTracking();

            // 每30秒模拟一次位置更新（仅用于演示）
            setInterval(simulateLocationUpdate, 30000);
        });

        // 游记生成功能
        function generateTravelLog() {
            alert('✨ AI正在为您生成游记...\n\n请稍候，精彩的游记即将完成');
        }

        // 显示二维码
        function showQRCode() {
            alert('📱 订单二维码\n\n请在景区入口扫描此二维码');
        }
    </script>
</body>
</html>
