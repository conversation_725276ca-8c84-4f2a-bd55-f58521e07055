<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥科创旅游 - 探索科技与文化的魅力</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 科技渐变背景 */
        .tech-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .tech-gradient-blue {
            background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 轮播滚动 */
        .snap-x {
            scroll-snap-type: x mandatory;
        }
        .snap-start {
            scroll-snap-align: start;
        }
        
        /* 科技光效 */
        .tech-glow {
            box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
        }
        
        /* 动画效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }
        
        /* 安全区域 */
        .safe-area-top {
            padding-top: env(safe-area-inset-top);
        }
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部状态栏 -->
        <div class="tech-gradient text-white px-4 pt-12 pb-6 safe-area-top">
            <!-- 头部信息 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔬</span>
                    </div>
                    <div>
                        <div class="text-sm opacity-90">欢迎来到</div>
                        <div class="text-base font-bold">合肥科创旅游</div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔍</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🌐</span>
                    </button>
                </div>
            </div>
            
            <!-- 欢迎标语 -->
            <div class="text-center mb-4">
                <h1 class="text-xl font-bold mb-2">探索科技与文化的魅力</h1>
                <p class="text-sm opacity-90">借助AI大模型，打造国际知名科创旅游目的地</p>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 科创景点轮播 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden tech-glow">
                    <div class="relative">
                        <!-- 轮播容器 -->
                        <div class="flex hide-scrollbar overflow-x-auto snap-x" id="heroCarousel">
                            <!-- 科学岛 -->
                            <div class="w-full flex-shrink-0 relative snap-start" onclick="navigateToPage('科学岛')">
                                <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=200&fit=crop" 
                                     alt="科学岛" class="w-full h-48 object-cover">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                                <div class="absolute bottom-4 left-4 text-white">
                                    <h3 class="text-lg font-bold mb-1">科学岛</h3>
                                    <p class="text-sm opacity-90">世界级科研基地，探索前沿科技</p>
                                </div>
                                <div class="absolute top-4 right-4 bg-blue-500 text-white px-2 py-1 rounded-full text-xs">
                                    🔬 科研重地
                                </div>
                            </div>
                            
                            <!-- 创新馆 -->
                            <div class="w-full flex-shrink-0 relative snap-start" onclick="navigateToPage('创新馆')">
                                <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop" 
                                     alt="创新馆" class="w-full h-48 object-cover">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                                <div class="absolute bottom-4 left-4 text-white">
                                    <h3 class="text-lg font-bold mb-1">合肥创新馆</h3>
                                    <p class="text-sm opacity-90">科技成果展示，创新文化体验</p>
                                </div>
                                <div class="absolute top-4 right-4 bg-purple-500 text-white px-2 py-1 rounded-full text-xs">
                                    🚀 创新展示
                                </div>
                            </div>
                            
                            <!-- 文化遗产 -->
                            <div class="w-full flex-shrink-0 relative snap-start" onclick="navigateToPage('文化遗产')">
                                <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop" 
                                     alt="文化遗产" class="w-full h-48 object-cover">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                                <div class="absolute bottom-4 left-4 text-white">
                                    <h3 class="text-lg font-bold mb-1">徽风皖韵</h3>
                                    <p class="text-sm opacity-90">楚汉文化，桐城文化传承</p>
                                </div>
                                <div class="absolute top-4 right-4 bg-amber-500 text-white px-2 py-1 rounded-full text-xs">
                                    🏛️ 文化传承
                                </div>
                            </div>
                        </div>
                        
                        <!-- 轮播指示器 -->
                        <div class="absolute bottom-4 right-4 flex space-x-1">
                            <div class="w-2 h-2 bg-white rounded-full opacity-100"></div>
                            <div class="w-2 h-2 bg-white rounded-full opacity-50"></div>
                            <div class="w-2 h-2 bg-white rounded-full opacity-50"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能推荐模块 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-gray-800">🤖 AI智能推荐</h2>
                    <button class="text-blue-500 text-sm">查看更多</button>
                </div>
                
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 tech-gradient-blue rounded-full flex items-center justify-center float-animation">
                            <span class="text-white">🧠</span>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">为您推荐今日行程</div>
                            <div class="text-sm text-gray-600">基于您的兴趣和天气情况</div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 rounded-lg p-3 mb-3">
                        <div class="text-sm font-medium text-blue-800 mb-1">🌤️ 今日天气适宜，推荐户外科创体验</div>
                        <div class="text-xs text-blue-600">科学岛 → 创新馆 → 巢湖科技园</div>
                    </div>
                    
                    <button class="w-full bg-blue-500 text-white py-2 rounded-lg text-sm font-medium card-hover">
                        查看完整行程
                    </button>
                </div>
            </div>

            <!-- 快捷服务入口 -->
            <div class="px-4 mb-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">⚡ 快捷服务</h2>
                <div class="grid grid-cols-4 gap-4">
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center" onclick="navigateToPage('科创探索')">
                        <div class="w-12 h-12 tech-gradient-blue rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🔬</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">科创探索</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center" onclick="navigateToPage('智能行程')">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">📅</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">智能行程</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center" onclick="navigateToPage('VR体验')">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🥽</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">VR体验</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center" onclick="navigateToPage('知识问答')">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🧩</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">知识问答</span>
                    </button>
                </div>
            </div>

            <!-- 实时资讯 -->
            <div class="px-4 mb-20">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-gray-800">📰 实时资讯</h2>
                    <button class="text-blue-500 text-sm">更多资讯</button>
                </div>
                
                <div class="space-y-3">
                    <div class="bg-white rounded-2xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1559028006-448665bd7c7f?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800 text-sm mb-1">科学岛新增量子科技体验馆</div>
                                <div class="text-xs text-gray-600 mb-2">探索量子世界的奥秘，感受前沿科技魅力</div>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>🕐 2小时前</span>
                                    <span>👁️ 1.2k</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-2xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800 text-sm mb-1">AI导览服务全面升级</div>
                                <div class="text-xs text-gray-600 mb-2">支持多语言实时翻译，国际游客体验更佳</div>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>🕐 5小时前</span>
                                    <span>👁️ 856</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-blue-500 text-xl mb-1">🏠</span>
                    <span class="text-xs text-blue-500 font-medium">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('科创探索')">
                    <span class="text-gray-400 text-xl mb-1">🔬</span>
                    <span class="text-xs text-gray-400">科创探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('智能行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">智能行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('互动社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">互动社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('个人中心')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">个人中心</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '科创探索': '科创探索.html',
                '智能行程': '智能行程.html',
                '互动社区': '互动社区.html',
                '个人中心': '个人中心.html',
                '科学岛': '科学岛.html',
                '创新馆': '创新馆.html',
                '文化遗产': '文化遗产.html',
                'VR体验': 'VR体验.html',
                '知识问答': '知识问答.html'
            };
            
            if (pageMap[pageName]) {
                // 模拟页面跳转
                console.log(`导航到: ${pageName} (${pageMap[pageName]})`);
                alert(`即将跳转到: ${pageName}`);
            }
        }
        
        // 轮播功能
        let currentSlide = 0;
        const carousel = document.getElementById('heroCarousel');
        const indicators = document.querySelectorAll('.absolute.bottom-4.right-4 .w-2');
        
        function updateIndicators() {
            indicators.forEach((indicator, index) => {
                if (index === currentSlide) {
                    indicator.style.opacity = '1';
                } else {
                    indicator.style.opacity = '0.5';
                }
            });
        }
        
        // 监听滚动事件更新指示器
        carousel.addEventListener('scroll', function() {
            const scrollLeft = this.scrollLeft;
            const itemWidth = this.offsetWidth;
            currentSlide = Math.round(scrollLeft / itemWidth);
            updateIndicators();
        });
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
